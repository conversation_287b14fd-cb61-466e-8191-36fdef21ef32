import { subscriptionMsgManagementApisWhite } from '@/api/modules/subscriptionMsgManagement';
import { tplMsgManagementApisWhite } from '@/api/modules/tplMsgManagement';
import http from '@/axiosConfig/http';
/**
 * Method of copying objects
 * @param {*Object} pre The initial object that needs to be copied
 * @param {*Object} next The copied target object
 * @returns Object 
 */
export function copyProperty(pre, next) {
  const temp = {};
  Object.keys(pre).forEach(key => {
    temp[key] = next[key] === undefined ? pre[key] : next[key];
  });
  return temp;
}
/**
 * Check whether the application official account or applet required by the currently requested service exists
 * @param {*String} curRequestApi Request interface address
 * @returns Boolean
 */
export function detectCurrentApplication(curRequestApi) {
  let currApplicationInfo = sessionStorage.getItem('currApplicationInfo');
  const { type } = currApplicationInfo ? JSON.parse(currApplicationInfo) : {};
  let whiteListPass = subscriptionMsgManagementApisWhite.list.some(api => curRequestApi.includes(api));
  if (whiteListPass) {
    return type == subscriptionMsgManagementApisWhite.type;
  } else {
    let whiteList2Pass = tplMsgManagementApisWhite.list.some(api => curRequestApi.includes(api));
    if (whiteList2Pass) {
      return type == tplMsgManagementApisWhite.type;
    } else {
      return true;
    }
  }
}

/**
 * Method for downloading files
 * @param {*String} url Download link
 * @param {*String} fileName Name the downloaded file
 */
export function downloadFile(url, fileName = 'sendReportfile') {
  http
    .get(url)
    .then(response => {
      const url = window.URL.createObjectURL(new Blob(["\ufeff",response.data],{type: 'text/plain;charset=utf-8'}));
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', `${fileName}.csv`);
      document.body.appendChild(link);
      link.click();
      link.parentNode.removeChild(link);
      window.URL.revokeObjectURL(url);
    })
    .catch(error => {
      console.error(error);
    });
}
