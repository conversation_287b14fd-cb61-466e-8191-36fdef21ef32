@import './themeUi.scss';
// Layout Universal UI
#wxmsgcenterapp {
  .general-layout-wrapper {
    position: relative;
    height: 100%;
    width: 100%;
    display: flex;
    overflow: hidden;
    .general-layout-main-container {
      flex: 1;
      display: flex;
      box-sizing: border-box;
      flex-direction: column;
      background: $wxmsgcenterThemeMainBg;
    }
    .general-app-main-wrapper {
      display: flex;
      flex: 1;
      height: 100%;
      overflow: hidden;
    }
  }
  .general-theme-color {
    color: $wxmsgcenterThemeColor;
  }
  .general-theme-bg {
    background: $wxmsgcenterThemeColor !important;
  }
  .genelal-theme-card-bg {
    background: white;
  }
  //Unified processing of packages wrapped in container.vue container with antd and other special generic styles, retaining the original antd style for non class names
  .global-wrapper-h {
    .#{$wxmsgcenterPrefix}-tabs-nav {
      margin: 0 20px;
      background: white;
      padding: 15px;
      .#{$wxmsgcenterPrefix}-tabs-nav-wrap {
        height: 30px;
        .#{$wxmsgcenterPrefix}-tabs-tab {
          border-bottom: none;
        }
      }
    }

    .global-container-content-margin {
      margin-left: 20px;
      margin-right: 20px;
    }
    .global-container-footer {
      border: 20px solid #f2f2f2;
      background: white;
      border-top: none;
      padding: 15px;
    }
  }
  .general-dropdown-item {
    margin: 5px;
    width: 100px;
    height: 20px;
    text-align: center;
    cursor: pointer;
    &:hover {
      background-color: $wxmsgcenterThemeColor;
      border-radius: 30px;
      color: #fff;
    }
  }

  /* link */
  .general-link-to {
    font-size: 12px;
    color: #3779b5;
    margin-right: 10px;
    cursor: pointer;
  }

  .general-padding20 {
    padding: 20px;
  }
  .general-mn {
    margin: 20px;
  }
  .general-ml20 {
    margin-left: 20px;
  }
  .general-mt20 {
    margin-top: 20px;
  }
  .general-mb20 {
    margin-bottom: 20px;
  }
  .general-mr20 {
    margin-right: 20px;
  }
  .general-ml0 {
    margin-left: 10px;
  }
  .general-float-right {
    float: right;
  }
  .general-translate-center {
    top: 50%;
    left: 50%;
    -webkit-transform: translate(-50%, -50%);
    -moz-transform: translate(-50%, -50%);
    -ms-transform: translate(-50%, -50%);
    -o-transform: translate(-50%, -50%);
    transform: translate(-50%, -50%);
  }
  .general-spin-area {
    position: absolute;
    top: 50%;
    width: 100%;
    z-index: 9;
    left: 50%;
    transform: translate(-50%, -50%);
    height: 100%;
    background: rgb(255 255 255 / 25%);
    justify-content: center;
    align-items: center;
    z-index:99999
  }
  .general-border-top {
    border-top: 1px solid $wxmsgcenterThinLineColor;
  }
  .general-search-notfixed {
    border-bottom: 20px solid $wxmsgcenterThemeMainBg;
  }
  .general-search-fixed {
    margin-top: -20px !important;
    &::after {
      content: '';
      position: absolute;
      width: 100%;
      top: 0;
      left: 0;
      height: 1px;
      background: $wxmsgcenterThinLineColor;
    }
  }
  // Create a universal UI for the form section
  .general-form-area {
    padding: 30px;
    padding-top: 50px;
    background: #fff;
    .#{$wxmsgcenterPrefix}-form-item {
      margin-bottom: 24px;
    }
    .#{$wxmsgcenterPrefix}-form-item-label {
      // width: 145px;
      width:189px;
    }
    .#{$wxmsgcenterPrefix}-select,
    .#{$wxmsgcenterPrefix}-input,
    .#{$wxmsgcenterPrefix}-picker,
    .#{$wxmsgcenterPrefix}-textarea,
    .#{$wxmsgcenterPrefix}-input-affix-wrapper {
      width: 400px;
    }
    .#{$wxmsgcenterPrefix}-date-editor.#{$wxmsgcenterPrefix}-input {
      width: 400px;
    }
  }
  .general-form-submit-btns {
    background: white;
    padding: 20px;
    border-top: 1px dashed $wxmsgcenterThinLineColor;
    padding-left: 176px;
    button {
      margin-right: 20px;
      padding: 0 27px;
    }
  }

  // Customize global search component
  .general-query-warrper {
    .#{$wxmsgcenterPrefix}-form-row {
      display: block;
      .#{$wxmsgcenterPrefix}-form-item-label {
        label {
          color: #949494;
        }
      }
    }
    .general-query-item {
      .#{$wxmsgcenterPrefix}-input-affix-wrapper,
      .#{$wxmsgcenterPrefix}-picker,
      .#{$wxmsgcenterPrefix}-select {
        min-width: 350px;
      }
    }
  }
  .general-theme-warning-color {
    color: #a12117;
  }

  .general-title-wrapper {
    padding: 20px;
    margin: 20px;
    margin-bottom: 0;
    border-bottom: 1px solid #ededed;
    background: white;
    h3 {
      .anticon-arrow-left {
        margin-right: 20px;
        cursor: pointer;
      }
      font-size: 17px;
      color: black;
      font-weight: bold;
    }
    .general-wrapper-tips {
      margin-top: 8px;
      font-size: 13px;
      line-height: 20px;
      color: #3c5876;
    }
  }
  .general-select-bg-w {
    .#{$wxmsgcenterPrefix}-select-selector {
      min-width: 100px !important;
    }
  }
  .general-radio-group-h {
    .#{$wxmsgcenterPrefix}-radio-button-wrapper {
      padding-inline: 7px;
      padding-block: 0px;
      font-size: 12px;
      line-height: 30px;
      border: 1px solid rgb(217, 217, 217);
      border-block-start-width: 1.02px;
      border-inline-width: 0px 1px;
      &:first-child {
        border-inline-start: 1px solid rgb(217, 217, 217);
        border-start-start-radius: 2px;
        border-end-start-radius: 2px;
      }
    }
  }
  .search-form-area-h{
     .ant-form-item{
      margin-bottom:0 !important;
     }
  }
}
