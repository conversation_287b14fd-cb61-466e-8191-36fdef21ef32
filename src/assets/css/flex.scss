.flex-right-center {
  display: -webkit-flex;
  display: flex;
  align-items: center;
  -webkit-align-items: center;
  justify-content: flex-end;
  -webkit-justify-content: flex-end;
}
.flex-left-center {
  display: -webkit-flex;
  display: flex;
  align-items: center;
  justify-content: flex-start;
}
.flex-center {
  display: -webkit-flex;
  display: flex;
  align-items: center;
  -webkit-align-items: center;
  justify-content: center;
  -webkit-justify-content: center;
}
.flex-right-left {
  display: -webkit-flex;
  display: flex;
  align-items: center;
  -webkit-align-items: center;
  justify-content: space-between;
  -webkit-justify-content: space-between;
}
.flex-right-left-nocontent {
  display: -webkit-flex;
  display: flex;
  justify-content: space-between;
  -webkit-justify-content: space-between;
}
.flex-wrap {
  display: -webkit-flex; /* Safari */
  display: flex;
  align-items: center;
  -webkit-align-items: center;
  justify-content: space-between;
  -webkit-justify-content: space-between;
  flex-wrap: wrap;
  -webkit-flex-wrap: wrap;
}
.flex-wrap-left {
  display: -webkit-flex; /* Safari */
  display: flex;
  align-items: center;
  justify-content: flex-start;
  flex-wrap: wrap;
}
.flex-wrap-right {
  display: -webkit-flex; /* Safari */
  display: flex;
  align-items: center;
  justify-content: flex-end;
  flex-wrap: wrap;
}

.flex1 {
  flex: 1;
}
.font-ell {
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
}
.gray {
  -webkit-filter: grayscale(100%);
  -moz-filter: grayscale(100%);
  -ms-filter: grayscale(100%);
  -o-filter: grayscale(100%);
  filter: grayscale(100%);
  filter: gray;
}
.flex-column {
  display: flex;
  flex-direction: column;
}
.flex {
  display: flex;
}
.flex-end {
  display: flex;
  justify-content: flex-end;
}
.flex-1 {
  flex: 1;
}
.flex-between {
  display: flex;
  justify-content: space-between;
}
.flex-wrap {
  flex-wrap: wrap;
}
.items-end {
  align-items: flex-end;
}
.items-center {
  align-items: center;
}
