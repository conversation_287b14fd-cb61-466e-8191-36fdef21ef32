@import './themeUi.scss';
// antd-ui, Global style adjustment
#wxmsgcenterapp {
  .system-menu-wrapper {
    //Menu container width
    width: 220px;
    .menu-item-name,
    .#{$wxmsgcenterPrefix}-menu-submenu-arrow {
      display: block;
    }
  }
  .system-menu-wrapper-collapse {
    // Menu Collapse
    width: 80px;
    .menu-item-name,
    .#{$wxmsgcenterPrefix}-menu-submenu-arrow {
      display: none;
    }
    .sub-menu-cusmt-img {
      margin-top: 5px;
    }
  }

  .#{$wxmsgcenterPrefix}-menu {
    background: $wxmsgcenterThemeColor;
    .#{$wxmsgcenterPrefix}-menu-sub {
      background: $wxmsgcenterThemesubMenuHighlightBarBg !important;
    }
    .#{$wxmsgcenterPrefix}-menu-title-content,
    .#{$wxmsgcenterPrefix}-menu-submenu-arrow {
      color: $wxmsgcenterThemeMenuFontColor;
    }
    .#{$wxmsgcenterPrefix}-menu-submenu-selected {
      .#{$wxmsgcenterPrefix}-menu-submenu-arrow {
        color: $wxmsgcenterThemeMenuHighlightBarBg !important;
      }
    }
    .#{$wxmsgcenterPrefix}-menu-submenu-active {
      .#{$wxmsgcenterPrefix}-menu-submenu-title {
        background-color: $wxmsgcenterThemeColor !important;
      }
    }
    .#{$wxmsgcenterPrefix}-menu-item-selected {
      background-color: $wxmsgcenterThemeMenuHighlightBarBg !important;
      border-radius: 2px;
    }
  }

  // form ,Unified style related to forms
  .#{$wxmsgcenterPrefix}-picker-range {
    border: 1px solid #d9d9d9;
    padding: 5px 11px 5px;
  }
  .#{$wxmsgcenterPrefix}-picker,
  .#{$wxmsgcenterPrefix}-picker-range,
  .#{$wxmsgcenterPrefix}-input,
  .#{$wxmsgcenterPrefix}-select-selector {
    min-height: 32px;
    border-radius: 3px;
    min-width: 200px;
  }
  .#{$wxmsgcenterPrefix}-picker {
    border: 1px solid rgb(217, 217, 217);
    padding: 5px 11px;
  }
  .#{$wxmsgcenterPrefix}-form-item {
    margin-bottom: 24px !important;
  }
  .#{$wxmsgcenterPrefix}-input-affix-wrapper {
    min-width: 200px;
  }
  .#{$wxmsgcenterPrefix}-form-item-label > label {
    font-size: 13px !important;
    height: 32px !important;
    color: #666666;
  }
  .#{$wxmsgcenterPrefix}-input-affix-wrapper {
    padding: 0 10px !important;
    border: 1px solid #d9d9d9;
  }
  .#{$wxmsgcenterPrefix}-input:focus,
  .#{$wxmsgcenterPrefix}-input-focused,
  .#{$wxmsgcenterPrefix}-select-focused .#{$wxmsgcenterPrefix}-select-selector,
  .#{$wxmsgcenterPrefix}-picker-focused,
  .#{$wxmsgcenterPrefix}-input-focused,
  .#{$wxmsgcenterPrefix}-input-affix-wrapper-focused {
    box-shadow: none !important;
  }

  .#{$wxmsgcenterPrefix}-form-item .#{$wxmsgcenterPrefix}-form-item-label > label::after {
    content: '';
  }
  // Button
  .#{$wxmsgcenterPrefix}-btn-link {
    color: #000000;
  }
  // table
  .#{$wxmsgcenterPrefix}-table-container{
    border:none !important
  }
  .#{$wxmsgcenterPrefix}-table-thead tr th {
    background: #f1f1f1 !important;
    border-right: 1px solid #e8e8e8 !important;
    border-bottom: 1px solid #e8e8e8 !important;
  }
  .#{$wxmsgcenterPrefix}-table-tbody tr td {
    border-right: 1px solid #e8e8e8 !important;
  }
  .#{$wxmsgcenterPrefix}-table-tbody > tr > td {
    border-bottom: 1px solid #e8e8e8 !important;
    border-top: none !important;
    padding: 12px 10px !important;
  }
  .#{$wxmsgcenterPrefix}-table-thead tr th:last-child,
  .#{$wxmsgcenterPrefix}-table-tbody tr td:last-child {
    border-right: none !important;
  }

  // modal
  .#{$wxmsgcenterPrefix}-modal {
    margin: 0 auto;
    .#{$wxmsgcenterPrefix}-modal-header {
      padding: 20px !important;
      .#{$wxmsgcenterPrefix}-modal-title {
        font-size: 17px !important;
      }
    }
    .#{$wxmsgcenterPrefix}-modal-content {
      padding: 0 !important;
    }
    .#{$wxmsgcenterPrefix}-modal-body {
      max-height: 75vh !important;
      overflow: auto;
      padding: 24px !important;
    }
    .#{$wxmsgcenterPrefix}-modal-footer {
      padding: 20px !important;
      .#{$wxmsgcenterPrefix}-btn-default{
        border: none;
        box-shadow: none;
      }
    }
    .#{$wxmsgcenterPrefix}-modal-close {
      top: 20px !important;
      right: 20px !important;
    }
  }

  // Time component
  .#{$wxmsgcenterPrefix}-picker-dropdown .#{$wxmsgcenterPrefix}-picker-cell-in-view.#{$wxmsgcenterPrefix}-picker-cell-in-range::before {
    background: $wxmsgcenterThemeColor;
  }
  .#{$wxmsgcenterPrefix}-picker-dropdown .#{$wxmsgcenterPrefix}-picker-cell-in-view.#{$wxmsgcenterPrefix}-picker-cell-range-start:not(.#{$wxmsgcenterPrefix}-picker-cell-range-start-single)::before,
  .#{$wxmsgcenterPrefix}-picker-dropdown .#{$wxmsgcenterPrefix}-picker-cell-in-view.#{$wxmsgcenterPrefix}-picker-cell-range-end:not(.#{$wxmsgcenterPrefix}-picker-cell-range-end-single)::before {
    background: $wxmsgcenterThemeColor !important;
  }
  .#{$wxmsgcenterPrefix}-picker-dropdown .#{$wxmsgcenterPrefix}-picker-cell-in-range {
    color: white !important;
  }

  .#{$wxmsgcenterPrefix}-picker-dropdown
    .#{$wxmsgcenterPrefix}-picker-time-panel-column
    > li.#{$wxmsgcenterPrefix}-picker-time-panel-cell-selected
    .#{$wxmsgcenterPrefix}-picker-time-panel-cell-inner {
    background: #f5ede5 !important;
  }
  // Drop down box
  .#{$wxmsgcenterPrefix}-select-dropdown .#{$wxmsgcenterPrefix}-select-item-option-selected:not(.#{$wxmsgcenterPrefix}-select-item-option-disabled) {
    background: #f5ede5 !important;
  }
  .#{$wxmsgcenterPrefix}-select-dropdown .#{$wxmsgcenterPrefix}-select-item-option-selected .#{$wxmsgcenterPrefix}-select-item-option-disabled {
    background: #f5ede5 !important;
  }
  // Global style secondary adjustment of modal.confirm
  .warning-prompt-box-h {
    width: 400px;
    top: 35vh;
    .#{$wxmsgcenterPrefix}-modal-confirm-title {
      display: none !important;
    }
    .anticon-exclamation-circle {
      display: none !important;
    }
    .#{$wxmsgcenterPrefix}-modal-confirm-content {
      padding-left: 30px !important;
      position: relative;
      padding: 15px 0;
      margin-top: 10px;
      font-size: 14px !important;
      padding-top: 20px;
    }
    .#{$wxmsgcenterPrefix}-modal-confirm-btns {
      padding-top: 15px;
      border-top: 1px solid $wxmsgcenterThinLineColor;
      border-style: dashed;
    .#{$wxmsgcenterPrefix}-btn-default{
      border: none;
      box-shadow: none;
      }
    }
  }
  .warning-prompt-box-h .#{$wxmsgcenterPrefix}-modal-confirm-content::before {
    position: absolute;
    content: '';
    left: 0;
    width: 20px;
    height: 20px;
    background-image: url('https://ka.etocdn.cn/26638/idc_beijing_org_414_brand_73/warning_icon.png');
  }

  // Customize upload container size
  .#{$wxmsgcenterPrefix}-upload-wrapper.#{$wxmsgcenterPrefix}-upload-picture-card-wrapper .#{$wxmsgcenterPrefix}-upload.#{$wxmsgcenterPrefix}-upload-select {
    width: 100% !important;
    height: 100% !important;
    border: none !important;
  }

  // Radio ,Single choice button combination mode
  .#{$wxmsgcenterPrefix}-radio-button-wrapper-checked:not(.#{$wxmsgcenterPrefix}-radio-button-wrapper-disabled) {
    background: black;
    color: white;
    border-color:black !important
  }
  .#{$wxmsgcenterPrefix}-radio-button-wrapper-checked:not(.#{$wxmsgcenterPrefix}-radio-button-wrapper-disabled):hover {
    color: white;
  }

  .#{$wxmsgcenterPrefix}-progress .#{$wxmsgcenterPrefix}-progress-success-bg,
  .#{$wxmsgcenterPrefix}-progress .#{$wxmsgcenterPrefix}-progress-bg {
    background-color: $wxmsgcenterThemeColor;
  }

  // Segmented controller
  .#{$wxmsgcenterPrefix}-segmented-thumb-motion-appear-active {
    background: $wxmsgcenterThemeColor !important;
    color: $wxmsgcenterThemeMenuHighlightColor !important;
  }
  .#{$wxmsgcenterPrefix}-segmented .#{$wxmsgcenterPrefix}-segmented-item-selected {
    background: $wxmsgcenterThemeColor;
    color: $wxmsgcenterThemeMenuHighlightColor;
  }

  // paging
  .#{$wxmsgcenterPrefix}-pagination-prev {
    .#{$wxmsgcenterPrefix}-pagination-item-link {
      border: none !important;
    }
  }
  .#{$wxmsgcenterPrefix}-pagination-next {
    .#{$wxmsgcenterPrefix}-pagination-item-link {
      border: none !important;
    }
  }

  .#{$wxmsgcenterPrefix}-image .#{$wxmsgcenterPrefix}-image-img {
    height: 100% !important;
  }
}
