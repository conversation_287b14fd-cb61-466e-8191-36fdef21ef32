import http from '@/axiosConfig/http';
const VUE_APP_WECHATOA = process.env.VUE_APP_WECHATOA;
const VUE_APP_MINIWECHAT = process.env.VUE_APP_MINIWECHAT;
const VUE_APP_GLOB_API = process.env.VUE_APP_GLOB_API;
const urls = {
  miniWechatSave: `${VUE_APP_MINIWECHAT}/applications`,
  miniWechatList: `${VUE_APP_MINIWECHAT}/applications`,
  miniWechatDetail: `${VUE_APP_MINIWECHAT}/applications/`,
  wechaApplicationsSave: `${VUE_APP_WECHATOA}/applications`, //Access to official account
  wechaApplications: `${VUE_APP_WECHATOA}/applications`, //List of official account
  wechaApplicationsDetail: `${VUE_APP_WECHATOA}/applications/`, //Details of official account
  featchUserInfoAuthPermission: VUE_APP_GLOB_API + '/cus-api/auth-permission/v1/users' //Query user information during user status synchronization
};
export function miniWechatSave(params) {
  return http.post(urls.miniWechatSave, params);
}
export function miniWechatList(params) {
  return http.get(urls.miniWechatList, params);
}
export function miniWechatDetail(id) {
  return http.get(urls.miniWechatDetail + id);
}

export function wechaApplicationsSave(params) {
  return http.post(urls.wechaApplicationsSave, params);
}
export function wechaApplications(params) {
  return http.get(urls.wechaApplications, params);
}
export function wechaApplicationsDetail(id) {
  return http.get(urls.wechaApplicationsDetail + id);
}
export function featchUserInfoAuthPermission(params) {
  return http.get(urls.featchUserInfoAuthPermission, params);
}
