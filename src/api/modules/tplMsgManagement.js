import http from '@/axiosConfig/http';
const VUE_APP_WECHATOA_TPL = process.env.VUE_APP_WECHATOA_TPL;
const VUE_APP_SFMC_INTERGRATION = process.env.VUE_APP_SFMC_INTERGRATION;
const VUE_APP_GLOB_API = process.env.VUE_APP_GLOB_API;
const VUE_APP_WECHATOA = process.env.VUE_APP_WECHATOA;
const urls = {
  customTemplates: `${VUE_APP_WECHATOA_TPL}/custom-templates`, //Custom Template List
  templatesList: `${VUE_APP_WECHATOA_TPL}/templates`, //Template List/Synchronize Templates
  createTemplatePlans: `${VUE_APP_WECHATOA_TPL}/custom-templates`, //Add custom template/template preview
  templatePlansEdit: `${VUE_APP_WECHATOA_TPL}/custom-templates`, //Custom Template Editing
  detailTemplatePlans: `${VUE_APP_WECHATOA_TPL}/custom-templates/`, //Custom Template Details
  templatePlansDel: `${VUE_APP_WECHATOA_TPL}/custom-templates/`, //Custom Template Delete
  getPlanHistory: `${VUE_APP_WECHATOA_TPL}/custom-send-histories/`, //Get the current template and send a message
  templateTasksList: `${VUE_APP_WECHATOA_TPL}/custom-template-messages`, //Get sending history
  customTemplateMsgAction: VUE_APP_WECHATOA_TPL + '/custom-template-messages', //Official account message sending/monitoring/canceling/suspending/resuming
  templatesEdit: VUE_APP_WECHATOA_TPL + '/templates', //Edit English
  downloadError: VUE_APP_GLOB_API + VUE_APP_WECHATOA_TPL + '/custom-template-messages/', //Download error message
  offMaSendResultList: VUE_APP_SFMC_INTERGRATION + '/ma-send-result',
  featchAuthorityList: VUE_APP_WECHATOA + `/user-authority`,
  messageChannelList: VUE_APP_SFMC_INTERGRATION + '/template-message-channel'
};
export const tplMsgManagementApisWhite = {
  list: [
    VUE_APP_WECHATOA_TPL + '/templates',
    VUE_APP_WECHATOA_TPL + '/custom-templates',
    VUE_APP_WECHATOA_TPL + '/custom-template-messages',
    urls.offMaSendResultList,
    VUE_APP_SFMC_INTERGRATION + '/sfmc-message-logs'
  ],
  type: 'service'
};
export function offMaSendResultList(params) {
  return http.get(urls.offMaSendResultList, params);
}
export const offMaSendResultListDownload = VUE_APP_GLOB_API + VUE_APP_SFMC_INTERGRATION + '/ma-send-result';
export function customTemplates(params) {
  return http.get(urls.customTemplates, params);
}
export function templatesList(params) {
  return http.get(urls.templatesList, params);
}
export function checkTemplate(id) {
  return http.get(urls.templatesList + '/' + id);
}
export function createTemplatePlans(params) {
  return http.post(urls.createTemplatePlans, params);
}
export function templatePlansEdit(params) {
  return http.put(urls.templatePlansEdit, params);
}
export function detailTemplatePlans(planId) {
  return http.get(urls.detailTemplatePlans + planId);
}
export function templatePlansDel(id) {
  return http.delete(urls.templatePlansDel + id);
}
export function getPlanHistory(params) {
  return http.get(urls.getPlanHistory + params.planId, params);
}
export function templateTasksList(params) {
  return http.get(urls.templateTasksList, params);
}
export function customTemplateMsgAction(params) {
  const headers = {
    'Content-Type': 'application/x-www-form-urlencoded'
  };
  return http.post(urls.customTemplateMsgAction, params, headers);
}
export function templatesEdit(params) {
  return http.put(urls.templatesEdit, params);
}
export const downloadTemplate = VUE_APP_GLOB_API + VUE_APP_WECHATOA_TPL + '/templates/downloadTemplate';
export const downloadError = id => {
  return urls.downloadError + id;
};
export const downloadAuthorityList = VUE_APP_GLOB_API + urls.featchAuthorityList;
export function featchAuthorityList(params) {
  return http.get(urls.featchAuthorityList, params);
}
export function syncAuthorityList(params) {
  return http.put(urls.featchAuthorityList, params);
}
export function delAuthorityList(params) {
  return http.delete(urls.featchAuthorityList, params);
}
export function updateAuthorityList(params) {
  return http.post(urls.featchAuthorityList, params);
}
export function messageChannelList(params) {
  return http.get(urls.messageChannelList, params);
}
