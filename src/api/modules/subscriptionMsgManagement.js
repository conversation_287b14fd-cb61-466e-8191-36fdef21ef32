import http from '@/axiosConfig/http';
const VUE_APP_MINIWECHAT_TPL = process.env.VUE_APP_MINIWECHAT_TPL;
const VUE_APP_MINIWECHAT_MA = process.env.VUE_APP_MINIWECHAT_MA;
const VUE_APP_GLOB_API = process.env.VUE_APP_GLOB_API;
const VUE_APP_MINIWECHAT = process.env.VUE_APP_MINIWECHAT;
const urls = {
  miniappTemplatesList: VUE_APP_MINIWECHAT_TPL + `/templates`, //Mini Program Template, or ,Edit English
  miniappTemplatesCheckTemplate: VUE_APP_MINIWECHAT_TPL + `/templates/`, //Check if it is available
  templatePlansList: VUE_APP_MINIWECHAT_TPL + `/custom-templates`,
  miniappCustomTemplatesDetail: VUE_APP_MINIWECHAT_TPL + `/custom-templates/`,
  miniappCustomTemplatesSend: VUE_APP_MINIWECHAT_TPL + `/custom-template-messages`,
  miniappTemplateTasksList: VUE_APP_MINIWECHAT_TPL + `/custom-send-histories/`,
  miniappTemplatesUserList: VUE_APP_MINIWECHAT_TPL + `/subscribe-user`,
  maSendResultList: VUE_APP_MINIWECHAT_MA + '/ma-send-result',
  featchAuthorityList: VUE_APP_MINIWECHAT + `/user-authority`,
  miniMessageChannelList: VUE_APP_MINIWECHAT_MA + '/template-message-channel'
};
export const subscriptionMsgManagementApisWhite = {
  list: [VUE_APP_MINIWECHAT_TPL + '/templates', VUE_APP_MINIWECHAT_TPL + '/custom-templates', VUE_APP_MINIWECHAT_TPL + '/custom-template-messages', urls.maSendResultList],
  type: 'miniapp'
};
export function miniMessageChannelList(params) {
  return http.get(urls.miniMessageChannelList, params);
}
export function miniappTemplatesList(params) {
  return http.get(urls.miniappTemplatesList, params);
}
export function miniappTemplatesCheckTemplate(id) {
  return http.get(urls.miniappTemplatesCheckTemplate + id);
}

export function miniappTemplatesEdit(params) {
  return http.put(urls.miniappTemplatesList, params);
}
export function templatePlansList(params) {
  return http.get(urls.templatePlansList, params);
}

export function createCustomTemplate(params) {
  return http.post(urls.templatePlansList, params);
}

export function miniappCustomTemplatesEdit(params) {
  return http.put(urls.templatePlansList, params);
}
export function miniappCustomTemplatesDetail(id) {
  return http.get(urls.miniappCustomTemplatesDetail + id);
}

export function miniappCustomTemplatesDel(id) {
  return http.delete(urls.miniappCustomTemplatesDetail + id);
}

export function miniappCustomTemplatesPreview(params) {
  return http.post(urls.templatePlansList, params);
}
export function miniappCustomTemplatesSend(params) {
  const headers = {
    'Content-Type': 'application/x-www-form-urlencoded'
  };
  return http.post(urls.miniappCustomTemplatesSend, params, headers);
}
export const downloadTemplateFile = VUE_APP_GLOB_API + VUE_APP_MINIWECHAT_TPL + '/templates-task-example';

export function miniappTemplateTasksContinue(params) {
  return http.post(urls.miniappCustomTemplatesSend, params);
}

export function miniappTemplateTasksList(params) {
  return http.get(urls.miniappTemplateTasksList + params.id, { ...params, id: undefined });
}
export const miniappTemplateTasksdownloadError = VUE_APP_GLOB_API + VUE_APP_MINIWECHAT_TPL + '/custom-template-messages/';
export function miniappTemplatesUserList(params) {
  return http.get(urls.miniappTemplatesUserList, params);
}
export const miniappTemplatesDownload = ({ id, query }) => {
  return VUE_APP_GLOB_API + urls.miniappTemplatesUserList + `/${id}${query}`;
};
export function maSendResultList(params) {
  return http.get(urls.maSendResultList, params);
}
export const maSendResultListDownload = VUE_APP_GLOB_API + VUE_APP_MINIWECHAT_MA + '/ma-send-result';

export const downloadMiniAuthorityList = VUE_APP_GLOB_API + urls.featchAuthorityList;
export function miniFeatchAuthorityList(params) {
  return http.get(urls.featchAuthorityList, params);
}
export function miniSyncAuthorityList(params) {
  return http.put(urls.featchAuthorityList, params);
}
export function miniDelAuthorityList(params) {
  return http.delete(urls.featchAuthorityList, params);
}
export function miniUpdateAuthorityList(params) {
  return http.post(urls.featchAuthorityList, params);
}
