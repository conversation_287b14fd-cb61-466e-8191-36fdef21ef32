import http from '@/axiosConfig/http';
const VUE_APP_SFMC_INTERGRATION = process.env.VUE_APP_SFMC_INTERGRATION;
const VUE_APP_GLOB_API = process.env.VUE_APP_GLOB_API;

const urls = {
  recordList: `${VUE_APP_SFMC_INTERGRATION}/sfmc-message-logs`,
  download: VUE_APP_GLOB_API + VUE_APP_SFMC_INTERGRATION + '/sfmc-message-logs'
};
export function recordList(params) {
  return http.get(urls.recordList, params);
}
export const downloadApi = urls.download;
