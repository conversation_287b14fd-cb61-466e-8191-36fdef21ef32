import http from '@/axiosConfig/http';
const VUE_APP_WECHATOA = process.env.VUE_APP_WECHATOA;
const VUE_APP_MINIWECHAT = process.env.VUE_APP_MINIWECHAT;
const urls = {
  createSuppliersForm: `${VUE_APP_WECHATOA}/api-manage`,
  updateSuppliersForm: `${VUE_APP_WECHATOA}/api-manage`,
  getApis: `${VUE_APP_WECHATOA}/provider-configs`,
  deleteSuppliers: `${VUE_APP_WECHATOA}/api-manage/`,
  detail: `${VUE_APP_WECHATOA}/api-manage/`,
  suppliersList: `${VUE_APP_WECHATOA}/api-manage`,
  miniCreateSuppliersForm: `${VUE_APP_MINIWECHAT}/api-manage`,
  miniUpdateSuppliersForm: `${VUE_APP_MINIWECHAT}/api-manage`,
  miniGetApis: `${VUE_APP_MINIWECHAT}/provider-configs`,
  miniDeleteSuppliers: `${VUE_APP_MINIWECHAT}/api-manage/`,
  miniDetail: `${VUE_APP_MINIWECHAT}/api-manage/`,
  miniSuppliersList: `${VUE_APP_MINIWECHAT}/api-manage`,
};

export function createSuppliersForm(params) {
  return http.post(urls.createSuppliersForm, params);
}
export function updateSuppliersForm(params) {
  return http.put(urls.updateSuppliersForm, params);
}
export function getApis(params) {
  return http.get(urls.getApis, params);
}
export function deleteSuppliers(id) {
  return http.delete(urls.deleteSuppliers + id);
}
export function detail(id) {
  return http.get(urls.detail + id);
}
export function suppliersList(params) {
  return http.get(urls.suppliersList, params);
}
export function miniCreateSuppliersForm(params) {
  return http.post(urls.miniCreateSuppliersForm, params);
}
export function miniUpdateSuppliersForm(params) {
  return http.put(urls.miniUpdateSuppliersForm, params);
}
export function miniGetApis(params) {
  return http.get(urls.miniGetApis, params);
}
export function miniDeleteSuppliers(id) {
  return http.delete(urls.miniDeleteSuppliers + id);
}
export function miniDetail(id) {
  return http.get(urls.miniDetail + id);
}
export function miniSuppliersList(params) {
  return http.get(urls.miniSuppliersList, params);
}
