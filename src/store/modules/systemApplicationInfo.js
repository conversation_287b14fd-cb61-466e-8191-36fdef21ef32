import { wechaApplications, miniWechatList } from '@/api/modules/applicationManagement.js';
import { message } from 'ant-design-vue';
import { i18n } from '@/local/index';
const { t } = i18n().global;
const sysApplicationInfo = {
  state: {
    applicationList: [],
    currApplicationInfo: null
  },
  mutations: {
    SET_CURR_APPLICATION_INFO: (state, params) => {
      state.currApplicationInfo = params;
    },
    SET_APPLICATION_LIST: (state, data) => {
      state.applicationList = data;
    },
    CLEAR_ALL_SYS_APPLICATION_INFO: state => {
      state.currApplicationInfo = null;
      state.applicationList = [];
    }
  },
  actions: {
    // 1. Get the list of executable apps for users
    async getApplicationList({ state, commit }, path) {
      const filterRouterArr = ['/applicationManagement', '/controlManagement'];
      const filterRouterShow = filterRouterArr.some(item => item.includes(path || ''));

      const loginName = filterRouterShow ? '' : sessionStorage.getItem('mail'); // Pages containing filtered routing addresses retrieve all application data, otherwise retrieve the application data of the currently logged in user
      const [applicationsData, miniAppListData] = await Promise.all([wechaApplications({ loginName }), miniWechatList({ loginName })]);
      // 1.If the currently cached selected application does not exist in the corresponding application list, the cache needs to be cleared
      if ((state.currApplicationInfo?.type == 'service' && !applicationsData.data.length) || (state.currApplicationInfo?.type == 'miniapp' && !miniAppListData.data.length)) {
        commit('SET_CURR_APPLICATION_INFO', null);
        sessionStorage.removeItem('currApplicationInfo');
        sessionStorage.removeItem('appid');
      }
      let allApplicationList = [].concat(applicationsData.data).concat(miniAppListData.data); //  2.Merge the application list of official account and applet
      if (allApplicationList.length) {
        allApplicationList.forEach(el => {
          el.appTypeName = el.type == 'service' ? t('common.weChatOfficialAccount') : t('common.miniProgram');
          el.logo = el.logo || '';
          el.label = el.wechatName;
          el.value = el.id;
        });
        sessionStorage.setItem('applicationList', JSON.stringify(allApplicationList));
        commit('SET_APPLICATION_LIST', allApplicationList);
      } else {
        commit('SET_APPLICATION_LIST', []);
        commit('SET_CURR_APPLICATION_INFO', null);
        sessionStorage.removeItem('appid');
        sessionStorage.removeItem('currApplicationInfo');
        sessionStorage.removeItem('applicationList');
        message.error(t('common.applicationsEmpty'));
      }
    },
    setCurrApplicationInfo({ commit }, { applicationList, curType }) {
      let currApplicationData = null;
      const curServiceItem = applicationList.some(el => {
        if (el.type == curType) {
          currApplicationData = el;
          return true;
        }
      });
      if (curServiceItem) {
        sessionStorage.setItem('appid', currApplicationData.appid);
        sessionStorage.setItem('currApplicationInfo', JSON.stringify(currApplicationData));
        commit('SET_CURR_APPLICATION_INFO', currApplicationData);
      } else {
        sessionStorage.removeItem('appid');
        sessionStorage.removeItem('currApplicationInfo');
        commit('SET_CURR_APPLICATION_INFO', null);
        message.error(t('common.applicationsEmpty'));
      }
    },
    defaultCurApplicationData({ dispatch }, curPath) {
      const curType = curPath.includes('/offiaccount') ? 'service' : 'miniapp';
      let applicationList = sessionStorage.getItem('applicationList');
      applicationList = applicationList ? JSON.parse(applicationList) : [];
      let currApplicationInfo = sessionStorage.getItem('currApplicationInfo');
      currApplicationInfo = currApplicationInfo ? JSON.parse(currApplicationInfo) : {};
      const { type, appid } = currApplicationInfo;
      const findAppItem = applicationList.find(el => el.appid == appid);
      if (type != curType || !findAppItem) {
        dispatch('setCurrApplicationInfo', { applicationList, curType });
      }
    }
  }
};
export default sysApplicationInfo;
