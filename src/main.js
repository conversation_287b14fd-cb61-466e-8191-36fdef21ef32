import { createApp } from 'vue';
import App from './App.vue';
import store from '@/store/index.js';
import router from './router/index.js';
import * as directives from '@/utils/directive.js';
import 'ant-design-vue/dist/reset.css';
import '@/assets/css/reset.css';
import '@/assets/css/flex.scss';
import '@/assets/css/antdUI.scss';
import '@/assets/css/generalStyles.scss';
import '@/assets/css/themeUi.scss';
import globalComponents from '@/components';
import { componentsPlugin } from '@/components/registerGlobAntdComp.js';
import './router/routerPromission.js'; //Routing permission
import { usI18n } from '@/local/index.js';
let instance = null;
function render(props = {}) {
  instance = createApp(App);
  const { container } = props;
  if (!props.jumpTo) {
    window.WXAUTH = {
      jumpTo: async to => {
        await router.push(to);
      },
      replaceTo: async to => {
        await router.replace(to);
      }
    };
  }
  if (props?.isInPortal?.()) {
    router.listening = false;
  }
  // Register global instructions
  Object.keys(directives).forEach(key => {
    instance.directive(key, directives[key]);
  });
  instance.use(router);
  instance.use(store);
  instance.use(globalComponents);
  usI18n(instance);
  componentsPlugin(instance);
  instance.mount(container ? container.querySelector('#wxmsgcenterapp') : '#wxmsgcenterapp');
}
// Independently run micro applications
if (!window.__POWERED_BY_QIANKUN__) {
  render();
}
export async function bootstrap(props) {
  try {
    if (props?.isInPortal()) {
      window.WXAUTH = {
        ...props
      };
      let { accessToken } = await props.getAccessToken(),
        tenantId = await props.getTenantId(),
        portalUserInfoData = await props.getUserInfo();
      sessionStorage.setItem('accessToken', accessToken);
      sessionStorage.setItem('tenantId', tenantId);
      sessionStorage.setItem('mail', portalUserInfoData.mail);
      sessionStorage.setItem('portalUserInfoData', JSON.stringify(portalUserInfoData));
    }
  } catch (error) {
    console.error(error);
  }
}
export async function mount(props) {
  render(props);
}
export async function unmount() {
  instance.unmount();
  instance = null;
}
