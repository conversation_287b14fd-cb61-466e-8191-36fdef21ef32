import { i18n } from '@/local/index';
const { t } = i18n().global;
export default {
  400:t('enum.request400'),
  401: t('enum.request401'),
  403: t('enum.request403'),
  404:  t('enum.request404'),
  405:  t('enum.request405'),
  408:  t('enum.request408'),
  500:  t('enum.request500'),
  501: t('enum.request501'),
  502:  t('enum.request502'),
  503: t('enum.request503'),
  504:  t('enum.request504'),
  505:  t('enum.request505'),
  909:  t('enum.request900'),
  901: t('enum.request901'),
};
