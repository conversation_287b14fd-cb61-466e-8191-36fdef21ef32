import axios from './axios'; //example
import { i18n } from '@/local/index';
const { t } = i18n().global;
let instance = axios();
import { detectCurrentApplication } from '@/utils/function.js';
const getHeaderParams = headers => {
  const accessToken = sessionStorage.getItem('accessToken');
  const tenantId = sessionStorage.getItem('tenantId');
  const appid = sessionStorage.getItem('appid');
  const mail = sessionStorage.getItem('mail');
  const headerParams = {
    Authorization: 'Bearer ' + accessToken,
    'X-Tenant-Id': tenantId,
    'X-Email': mail,
    'X-CUS-Tenant-Id': tenantId,
    'X-CUS-EMPLOYEE-EMAIL': mail,
    appid
  };
  return headers ? { ...headerParams, ...headers } : headerParams;
};
export default {
  get(url, params, headers) {
    let options = {};
    options.headers = getHeaderParams(headers);
    if (params) {
      options.params = params;
    }
    if (detectCurrentApplication(url)) {
      return instance.get(url, options);
    } else {
      return Promise.reject({ msg: t('common.selectApplication')});
    }
  },
  post(url, params, headers) {
    let options = {};
    options.headers = getHeaderParams(headers);
    if (detectCurrentApplication(url)) {
      return instance.post(url, params, options);
    } else {
      return Promise.reject({ msg: t('common.selectApplication') });
    }
  },
  put(url, params, headers) {
    let options = {};
    options.headers = getHeaderParams(headers);
    return instance.put(url, params, options);
  },
  delete(url, params, headers) {
    let options = {};
    options.headers = getHeaderParams(headers);
    if (params) {
      options.params = params;
    }
    return instance.delete(url, options);
  }
};
