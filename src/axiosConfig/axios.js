import axios from 'axios';
import { message } from 'ant-design-vue';
import errorMsgEnum from './errorMsgEnum';
const GLOB_API = process.env.VUE_APP_GLOB_API;
let http = axios.create({
  timeout: 60000,
  baseURL: GLOB_API
});
axios.defaults.withCredentials = false;
const isObject = obj => {
  return typeof obj === 'object' && obj !== null && !Array.isArray(obj);
};
//By default, loading is loaded every time a request is made
http.interceptors.request.use(
  config => {
    if (config.data instanceof FormData && config.data.get('noLoading')) {
      config.data.delete('noLoading');
    } else if (config.params && 'noLoading' in config.params) {
      config.params.noLoading = undefined;
    } else if (config.data && 'noLoading' in config.data) {
      config.data.noLoading = undefined;
    } else {
      showLoading();
    }
    return config;
  },
  error => {
    hideLoading();
    if (error && error.stack.indexOf('timeout') > -1) {
      message.error(errorMsgEnum[909]);
    } else {
      message.error(error || errorMsgEnum[909]);
    }
    return Promise.reject(error);
  }
);

http.interceptors.response.use(
  response => {
    hideLoading();
    let { data } = response;
    const { errorCode, errorMessage, code } = isObject(data) ? data : { errorCode: 0, errorMessage: 'ok', code: 0 };
    if (!errorCode || code === 200) {
      return Promise.resolve(response);
    } else {
      message.error(errorMessage || errorMsgEnum[901]);
      console.error(`${response.request.responseURL}:${errorMessage || errorMsgEnum[901]}`);
      return Promise.reject(response);
    }
  },
  async error => {
    hideLoading();
    const { status , data } = error.response 
    error.message = data?.errorMessage || errorMsgEnum[status] || errorMsgEnum[901];
    if (status === 401) {
      message.error(error.message);
      await window.WXAUTH.logout();
    }
    message.error(error.message);
    return Promise.reject(error.response);
  }
);
function showLoading() {
  const spinDom = document.getElementsByClassName('general-spin-area');
  if (spinDom?.length) {
    if (!spinDom[0].attributes.style.value.includes('flex')) {
      spinDom[0].style.display = 'flex';
    }
  }
}
function hideLoading() {
  const spinHideDom = document.getElementsByClassName('general-spin-area');
  if (spinHideDom?.length) {
    if (!spinHideDom[0].attributes.style.value.includes('none')) {
      spinHideDom[0].style.display = 'none';
    }
  }
}
export default function () {
  return http;
}
