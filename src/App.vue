<template>
  <a-config-provider :locale="configLang" :get-container="getMessageContainer" :theme="{
    token: {
      colorPrimary: '#000000',
      fontSize: 12,
      borderRadius: 2,
      wireframe: true,
      size: 8
    }
  }">
    <transition name="fade" mode="out-in">
      <router-view v-if="isRouterAlive" />
    </transition>
  </a-config-provider>
</template>
<script setup>
import { ref, provide, nextTick, onMounted } from 'vue';
import zhCN from 'ant-design-vue/es/locale/zh_CN';
import enUS from 'ant-design-vue/es/locale/en_US';
let isRouterAlive = ref(true);
let configLang = ref(zhCN);
provide('reload', () => {
  isRouterAlive.value = false;
  nextTick(() => {
    isRouterAlive.value = true;
  });
});
const getMessageContainer = () => {
  return document.getElementById('wxmsgcenterapp');
};
const getCurSystemLang = () => {
  let curLang = 'zh-CN';
  if (window.WXAUTH?.getLang) {
    curLang = window.WXAUTH?.getLang();
  } else {
    curLang = navigator.language;
  }
  if (curLang === 'en-US') {
    configLang.value = enUS;
  } else {
    configLang.value = zhCN;
  }
}
onMounted(() => {
  getCurSystemLang()
});
</script>

<style lang="scss">
#wxmsgcenterapp {
  font-family: 'Avenir', Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  height: 100%;
  position: absolute;
  width: 100%;
  overflow: hidden;
}

::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  width: 6px;
  background: rgba(#999999, 0.1);
  -webkit-border-radius: 2em;
  -moz-border-radius: 2em;
  border-radius: 2em;
}

::-webkit-scrollbar-thumb {
  background-color: rgba(#999999, 0.5);
  background-clip: padding-box;
  min-height: 28px;
  -webkit-border-radius: 2em;
  -moz-border-radius: 2em;
  border-radius: 2em;
}

::-webkit-scrollbar-thumb:hover {
  background-color: rgba(#666666, 1);
}

.scrollIntoView {
  position: fixed;
  right: 30px;
  bottom: 100px;
  width: 50px;
  height: 50px;
  background: red;
}
</style>
