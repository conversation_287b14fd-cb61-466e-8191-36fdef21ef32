export default {
  oldTemplateMessage: '旧模板消息',
  oneTimeSubscription: '一次性订阅',
  longTermSubscription: '长期订阅',
  userAgreesToSubscribe:'用户同意订阅',
  userRefusesToSubscribe:'用户拒绝订阅',
  userUnsubscribes:'用户取消订阅',
  notUsed:'未使用',
  lockedIn:'锁定中',
  usedAlready:'已使用',
  thing:'20个以内字符,支持汉字、数字、字母或符号组合',
  number: '32位以内的数字,可带小数',
  letter: '32位以内的字母',
  symbol: '5位以内的符号',
  characterString: '32位以内的数字、字母或符号组合',
  time: '24小时制时间格式(支持+年月日)，支持填时间段，两个时间点之间用“~”符号连接。例如:15:01,或:2019年10月1日 15:01',
  date: '年月日格式(支持+24小时制时间),支持填时间段,两个时间点之间用“~”符号连接。例如:2019年10月1日,或:2019年10月1日 15:01',
  amount: '1个币种符号+10位以内纯数字,可带小数,结尾可带“元”,可带小数',
  phoneNumber: '17位以内的数字、符号,例:+86-0766-66888866',
  carNumber: '8位以内,第一位与最后一位可为汉字,其余为字母或数字,例如:粤A8Z888',
  name: '10个以内纯汉字或20个以内纯字母或符号,其中:文名10个汉字内;纯英文名20个字母内;中文和字母混合按中文名算,10个字内',
  phrase: '5个以内的汉字,例如:配送中',
  keyword:'200字符以内,支持汉字、数字、字母或符号组合',
  waitingConfirmationSend:'等待确认发送',
  sending:'发送中',
  WaitingSend:'等待发送',
  sendPause:'发送暂停',
  sendingCompleted:'发送完成',
  pauseInProgress:'暂停中',
  noReview:'无需审核',
  pendingApproval:'待审核',
  approved:'审核通过',
  reviewRejected:'审核拒绝',
  notSent:'未发送',
  hasBeenSent:'已发送',
  userHasBeenDeleted:'用户已删除',
  enablingInProgress:'启用中',
  request400: '400:语法错误,服务器无法处理请求',
  request401: '401:用户授权失败，请重新登录',
  request403: '403:拒绝访问',
  request404: '404:请求错误,未找到该资源',
  request405: '405:请求方法未允许',
  request408: '408:请求超时',
  request500: '500:服务器端出错',
  request501: '501:网络未实现',
  request502: '502:网关错误',
  request503: '503:服务不可用',
  request504: '504:网络超时',
  request505: '505:http版本不支持该请求',
  request909: '909:未知错误,请稍候重试',
  request901: '901:未知错误,连接终止',
  assessTokenFailed:'获取assessToken失败'
};
