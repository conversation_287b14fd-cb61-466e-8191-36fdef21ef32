export default {
  addSupplier: '添加供应商',
  updateSuppliers: '更新供应商',
  loseEfficacy: '失效',
  controlManagementDescribe: '中控管理用于为第三方供应商提供服务，支持添加多个供应商，可基于单个供应商灵活配置各模块的接口调用权限',
  activityName: '活动名称',
  ipAuthenticated: '是否IP认证',
  iPAddress: 'IP地址',
  periodOfValidity: '有效期',
  validityDate: '有效日期',
  webCallback: '网页授权回调',
  webCallbackTips: '网页授权域名接口必须添加安全域名才能生效',
  interfacePermissions: '接口权限',
  checkAll: '全选',
  appkeyTips: 'APPKEY已生成，本页面关闭后将不再显示APPKEY，请妥善保存。',
  appkeyTipsRed: '我已经了解APPKEY不会再显示并已复制保存好该APPKEY',
  phoneVerification: '请输入正确的手机号',
  weChatOfficialAccountManagementTitle:'公众号中控管理',
  miniProgramManagementTitle:'小程序中控管理'
};
