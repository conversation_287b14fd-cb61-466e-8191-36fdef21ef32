export default {
  wechatOfficialAccountTemplateManagement: '公众号模板管理',
  switchApplications: '切换应用',
  weChatMPTemplate: '微信MP模板',
  templateSearch: '模板搜索',
  templateSearchTips: '请输入模板名称进行搜索',
  synchronizeMPtemplate: '同步MP模板',
  checkIfItIsAvailable: '检查是否可用',
  editEnglish: '编辑英文',
  checkIfItIsAvailableTips: '您确定要检查模板是否可用吗？',
  synchronizeMPtemplateTips: '您确定要同步MP模板吗？',
  templateMessagePreviewTips: '请输入openid，多个openid请用 " ; " 隔开',
  newTemplateMessage: '新建模板消息',
  historyData: '历史数据',
  operationTipstTitle: '操作提示',
  operationTips: '目前的发送速率为10万人/小时,分批发送可加快发送速度。微信对模板消息发送有监管,不建议发送速度过快. 若发送时间超过当天24点,系统将名单剩余人员自动延迟到第二天早上8点继续发送',
  uploadTips: '注：csv表格文件，第一行不要有表头（从表格的第2列起可上传变量值，如新建模板消息时，【内容编辑】中加了含“2”的内容，则“2”就动态读取第2列的值）',
  uploadFiles: '上传文件',
  plannedSendTime: '计划发送时间',
  planSend: '计划发送',
  sendNow: '立即发送',
  updateTemplateMessage: '更新模板消息',
  updateTemplateMessagetTips:
    '模板消息仅用于公众号向用户发送重要的服务通知，只能用于符合其要求的服务场景中，如信用卡刷卡通知，商品购买成功通知等。不支持广告等营销类消息以及其它所有可能对用户造成骚扰的消息。如主动下发、内容涉及恶意营销、频率过高恶意骚扰、以及模板参数内容乱填写将被封接口处罚，责任将由品牌自行承担。',
  jumpAddress: '跳转地址',
  jumpAddressPlaceholder: '请输入点击模拟时跳转的地址',
  jumpAddressTips:
    '注：跳转地址和小程序跳转地址都是非必填字段，若都不传则模板无跳转；若都传，会优先跳转至小程序。开发者可根据实际需要选择其中一种跳转方式即可。当用户的微信客户端版本不支持跳小程序时，将会跳转至url',
  appIdTips: '该小程序appid必须与发模板消息的公众号是绑定关联关系，暂不支持小游戏',
  miniProgramJumpAddress: '小程序跳转地址',
  miniProgramJumpAddressTips: '所需跳转到小程序的具体页面路径，支持带参数,（示例index?foo=bar），暂不支持小游戏',
  templateEnglishTips: '该模板字段未编辑英文版',
  sendRecordOperate: '你确定你想要',
  deleteUserTips: '请选择需要移除的应用，移除后将无法查看应用信息',
  sfmcWechatOfficialAccountManagement: '公众号SFMC管理',
  miniProgramTemplateManagement: '小程序模板管理',
  myTemplate: '我的模板',
  subscriptionMessage: '订阅消息',
  createSubscriptionTemplate: '新建订阅消息模板',
  updateSubscriptionTemplate: '更新订阅消息模板',
  jumpMiniProgram: '是否跳转小程序',
  miniProgramPath: '小程序路径',
  lookMiniProgram: '进入小程序看看',
  refusalNotice: '拒收通知',
  subscriptionData: '订阅数据',
  downloadSubscriptionData: '下载订阅数据',
  downloadUnsubscriptionData: '下载取消订阅数据',
  totalNumberPeople: '总人数',
  subscribeEvents: '订阅事件',
  subscriptionTime: '订阅时间',
  dataDeduplication: '数据是否去重',
  subscriptionScenario: '订阅场景',
  templateIdDoesNotExist: '模板id不存在，请核实',
  sendSubscriptionMessage: '发送订阅消息',
  sendRange: '发送范围',
  uploadWhitelist: '上传白名单',
  sendEveryMinute: '每分钟发送',
  sendSpeed: '发送速度',
  oneUser: '个用户',
  whiteList: '白名单',
  pushAll: '全部推送',
  bySubscriptionTime: '按订阅时间',
  uploadWhiteTips1: '从第2列起可上传变量值，如推送内容中含“ 2 ”，就动态读取第2列的值',
  uploadWhiteTips2: '文件中若包含没有订阅的用户，发送消息时将会被忽略，发送记录里可下载失败数据',
  keyword: '关键词',
  templateDetailsTips: '在发送时，需要将内容中的参数（DATA内为参数）赋值替换为需要的信息',
  subscriptionMessagePreview: '订阅消息预览',
  openidPreviewTips: '请确认您已订阅该消息，否则预览失败'
};
