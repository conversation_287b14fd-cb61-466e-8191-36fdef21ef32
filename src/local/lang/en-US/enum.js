export default {
  oldTemplateMessage: 'Old Template Message',
  oneTimeSubscription: 'One Time Subscription',
  longTermSubscription: 'Long Term Subscription',
  userAgreesToSubscribe: 'User Agrees To Subscribe',
  userRefusesToSubscribe: 'User Refuses To Subscribe',
  userUnsubscribes:'User Unsubscribes',
  notUsed: 'Not Used',
  lockedIn: 'Locked In',
  usedAlready: 'Used Already',
  thing: 'Up To 20 Characters, Supporting Combinations Of Chinese Characters, Numbers, Letters, Or Symbols',
  number: 'Numbers Up To 32 Digits, With Decimals Allowed',
  letter: 'Letters Within 32 Bits',
  symbol: 'Symbols Within 5 Digits',
  characterString: 'Combination Of Numbers, Letters, Or Symbols Within 32 Digits',
  time: '24-Hour Time Format (Supports+Year Month Day), Supports Filling In Time Periods, And Connects Two Time Points With A "~" Symbol. For Example: 15:01, Or: 15:01 On October 1, 2019',
  date: 'Year Month Day Format (Supports+24-Hour Time), Supports Filling In Time Periods, And Connects Two Time Points With A "~" Symbol. For Example: October 1st, 2019, Or 15:01 On October 1st, 2019',
  amount: '1 Currency Symbol+Pure Number Up To 10 Digits, Can Include Decimals, Can End With "Yuan", Can Include Decimals',
  phoneNumber: 'Numbers And Symbols Within 17 Digits, For Example:+86-0766-66888866',
  carNumber: 'Within 8 Digits, The First And Last Digits Can Be Chinese Characters, And The Rest Can Be Letters Or Numbers, For Example: 粤 A8Z888',
  name: 'Within 10 Pure Chinese Characters Or 20 Pure Letters Or Symbols, Including: Within 10 Chinese Characters For The Title; Within 20 Letters Of A Pure English Name; Chinese And Alphabet Mixed According to Chinese Name, Within 10 Characters',
  phrase: 'Up To 5 Chinese Characters, For Example: In Transit',
  keyword: 'Within 200 Characters, Supporting Combinations Of Chinese Characters, Numbers, Letters, Or Symbols'
  ,waitingConfirmationSend:'Waiting For Confirmation To Send',
  sending:'Sending',
  WaitingSend:'Waiting Send',
  sendPause:'Send Pause',
  sendingCompleted:'Sending Completed',
  pauseInProgress:'Pause In Progress',
  noReview:'No Need For Review',
  pendingApproval:'Pending Approval',
  approved:'Approved',
  reviewRejected:'Review Rejected',
  notSent:'Not Sent',
  hasBeenSent:'Has Been Sent',
  userHasBeenDeleted:'User Has Been Deleted',
  enablingInProgress:'Enabling In Progress',
  request400: '400:Grammar Error, Server Cannot Process Request',
  request401: '401:User Authorization Failed, Please Log In Again',
  request403: '403:No Access',
  request404: '404:Request Error, The Resource Was Not Found',
  request405: '405:Request Method Not Allowed',
  request408: '408:Request Timeout',
  request500: '500:Server Side Error',
  request501: '501:Network Not Implemented',
  request502: '502:Bad Gateway',
  request503: '503:The Service Is Unavailable',
  request504: '504:Network Timeout',
  request505: '505:The HTTP Version Does Not Support This Request',
  request909: '909:Unknown Error, Please Try Again Later',
  request901: '901:Unknown Error, Connection Terminated',
  assessTokenFailed:'Failed To Obtain AssessToken'
};
