export default {
  wechatOfficialAccountTemplateManagement: 'WeChat Official Account Template Management',
  switchApplications: 'Switch Applications',
  weChatMPTemplate: 'WeChat MP Template',
  templateSearch: 'Template Search',
  templateSearchTips: 'Please Enter The Template Name Or Keyword To Search',
  synchronizeMPtemplate: 'Synchronize MP Template',
  checkIfItIsAvailable: 'Availiable Check',
  editEnglish: 'Edit English',
  checkIfItIsAvailableTips: 'Are You Sure You Want To Check If The Template Is Available?',
  synchronizeMPtemplateTips: 'Are You Sure You Want To Synchronize The MP Template?',
  templateMessagePreviewTips: 'Please Enter An Openid, Use ";" For Multiple Openids Separate',
  newTemplateMessage: 'New Template Message',
  historyData: 'History Data',
  operationTipstTitle: 'Operation Tips',
  operationTips:
    'The Current Sending Rate Is 100000 People/hour, And Sending In Batches Can Accelerate The Sending Speed. WeChat Has Regulations On Sending Template Messages And Does Not Recommend Sending Them Too Quickly If The Sending Time Exceeds 24:00 On The Same Day , The System Will Automatically Delay The Remaining Personnel On The List Until 8:00 Am The Next Day To Continue Sending',
  uploadTips:
    'Note: CSV Table File, Do Not Have A Header In The First Row (Variable Values Can Be Uploaded From The Second Column Of The Table. For Example, When Creating A Template Message And Adding Content Containing "2" In The [Content Editing], "2" Will Dynamically Read The Value In The Second Column)',

  uploadFiles: 'Upload Files',
  plannedSendTime: 'Plan Send Time',
  planSend: 'Plan To Send',
  sendNow: 'Send Now',
  updateTemplateMessage: 'Update Template Message',
  updateTemplateMessagetTips:
    'Template Messages Are Only Used To Send Important Service Notifications To Users On The Official Account, And Can Only Be Rsed In Service Scenarios That Meet Their Requirements, Such As Credit Card Swiping Notification, Notification Of Successful Purchase Of Goods, Etc. We Do Not Support Marketing Messages Such As Advertisements Or Any Other Messages That May Cause Harassment To Users. If The Content Is Actively Distributed, Involves Malicious Marketing, Engages In Excessive Frequency Of Malicious Harassment, Or Fills In Template Parameter Content Incorrectly, The Interface Will Be Blocked And The Responsibility Will Be Borne By The Brand Itself.',
  jumpAddress: 'Jump Address',
  jumpAddressPlaceholder: 'Please Enter The Address To Be Redirected When Clicking On The Simulation',
  jumpAddressTips:
    "Note: The Jump Address And Mini Program Jump Address Are Both Non Mandatory Fields. If Neither Is Passed, The Template Will Not Have A Jump; If All Are Uploaded, It Will Be Redirected To The Mini Program First. Developers Can Choose One Of The Jump Methods According To Their Actual Needs. When The User's WeChat Client Version Does Not Support Jumping To Mini Programs, It Will Redirect To The URL",
  appIdTips: 'The Appid Of The Applet Must Be Bound To The WeChat Official Account That Sends The Template Message, And It Does Not Support Games Temporarily',
  miniProgramJumpAddress: 'Mini Program Jump Address',
  miniProgramJumpAddressTips: 'The Specific Page Path That Needs To Be Redirected To The Mini Program, Supports Parameters (e.g. index?foo=bar), Does Not Currently Support Mini Games',
  templateEnglishTips: 'This Template Field Has Not Been Edited In English',
  sendRecordOperate: 'Are You Sure You Want',
  deleteUserTips: 'Please Select The Application That Needs To Be Removed. After Removal, You Will Not Be Able To View The Application Information',
  sfmcWechatOfficialAccountManagement: 'SFMC Management Of WeChat Official Account',
  miniProgramTemplateManagement: 'Mini Program Template Management',
  myTemplate: 'My Template',
  subscriptionMessage: 'Subscription Message',
  createSubscriptionTemplate: 'Create Subscription Message Template',
  updateSubscriptionTemplate: 'Update Subscription Message Template',
  jumpMiniProgram: 'Jump To Mini Program',
  miniProgramPath: 'Mini Program Path',
  lookMiniProgram: 'View Mini Programs',
  refusalNotice: 'Refusal Notice',
  subscriptionData: 'Subscription Data',
  downloadSubscriptionData: 'Download Subscription Data',
  downloadUnsubscriptionData: 'Download And Unsubscribe Data',
  totalNumberPeople: 'Total Number People',
  subscribeEvents: 'Subscribe Events',
  subscriptionTime: 'Subscription Time',
  dataDeduplication: 'Data Deduplication',
  subscriptionScenario: 'Subscription Scenario',
  templateIdDoesNotExist: 'Template ID Does Not Exist',
  sendSubscriptionMessage: 'Send Subscription Message',
  sendRange: 'Send Range',
  uploadWhitelist: 'Upload Whitelist',
  sendEveryMinute: 'Send Every Minute',
  sendSpeed: 'Send Speed',
  oneUser: 'One, User',
  whiteList: 'White List',
  pushAll: 'Push All',
  bySubscriptionTime: 'By Subscription Time',
  uploadWhiteTips1: 'Starting From The Second Column, Variable Values Can Be Uploaded. If The Push Content Contains "2", The Value In The Second Column Will Be Dynamically Read',
  uploadWhiteTips2: 'If The File Contains Users Who Have Not Subscribed, It Will Be Ignored When Sending Messages, And The Download Failure Data Can Be Found In The Sending Record',
  keyword: 'Keyword',
  templateDetailsTips: 'When Sending, It Is Necessary To Replace The Parameter Values (In DATA) In The Content With The Required Information.',
  subscriptionMessagePreview: 'Subscription Message Preview',
  openidPreviewTips: 'Please Confirm That You Have Subscribed To This Message, Otherwise Preview Will Fail.'
};
