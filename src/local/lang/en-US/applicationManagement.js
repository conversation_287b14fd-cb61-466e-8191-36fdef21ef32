export default {
  addWeChatOfficialAccount: 'Add WeChat Official Account',
  addMiniProgram: 'Add Mini Program',
  wechatOfficialAccountUserAssignment: 'WeChat Official Account User Assignment',
  miniProgramUserAllocation: 'Mini Program User Allocation',
  updateWeChatOfficialAccount: 'Update WeChat Official Account',
  updateMiniProgram: 'Update Mini Program',
  sourceOfAppID: 'You Can Find The AppID In The Following Location After Logging In From mp-weixin.qq.com: Advanced Features > Development Mode>Developer Credentials > AppID column',
  sourceOfTheMiniProgramAppID: 'You Can Find The AppID At The Following Location After Logging In From mp-weixin.qq.com: Development > Development Settings > Developer ID',
  weChatOfficialAccountAvatar: 'WeChat Official Account Avatar',
  miniProgramAvatar: 'Mini Program Avatar',
  weChatOfficialAccountAvatarPrompt: 'Please Select WeChat Official Account Avatar',
  miniProgramAvatarPrompt: 'Please Select The Mini Program Avatar',
  weChatOfficialAccountDescription: 'WeChat Official Account Description',
  weChatOfficialAccountDescriptionPrompt: 'Please Enter WeChat Official Account Description',
  miniProgramDescriptionPrompt:'Mini Program Description Prompt',
  miniProgramDescription:'Mini Program Description',
  weChatAcSource:'Do you want to obtain AC from WeChat',
  serverAddress:'Server Address',
  originalId:'Original Id',
  originalIdPrompt:'You Can Find The Original ID At mp.weixin.qq.com After Logging In: WeChat Platform > Official Account Settings > Registration Information > original ID'
};
