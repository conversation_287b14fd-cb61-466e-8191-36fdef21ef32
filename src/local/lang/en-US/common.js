export default {
  view: 'View',
  status: 'Status',
  operate: 'Operate',
  number: 'Number',
  applicationName: 'Application Name',
  applicationType: 'Application Type',
  creationTime: 'Creation Time',
  save: 'Save',
  yes: 'Yes',
  no: 'No',
  cancel: 'Cancel',
  confirm: 'Confirm',
  close: 'Close',
  delete: 'Delete',
  removes: 'Removes',
  add: 'Add',
  edit: 'Edit',
  search: 'Search',
  reset: 'Reset',
  pleaseEnter: 'Please Enter',
  pleaseSelect: 'Please Select',
  copy: 'Copy',
  prompt: 'Prompt',
  operationTips: 'Are You Sure You Want To Perform This Operation?',
  addUserTips: 'Are You Sure You Want To Add This User?',
  operationSuccessful: 'Operation Successful',
  copySuccess: 'Copy Success',
  startDate: 'Start Date',
  strip: 'Strip',
  page: 'Page',
  total: 'Total',
  jumpTo: 'Jump To',
  download: 'Download',
  weChatOfficialAccount: 'WeChat Official Account',
  miniProgram: 'Mini Program',
  user: 'User',
  customTemplate: 'Custom Template',
  sendRecords: 'Send Records',
  title:'Title',
  templateTitle: 'Template Title',
  templateId: 'Template ID',
  synchronizationTime: 'Synchronization Time',
  details: 'Details',
  serialNumber: 'Serial Number',
  name: 'Name',
  taskNumber: 'Task Number',
  sendQuantity: 'Send Quantity',
  supplierName: 'Supplier Name',
  contacts: 'Contacts',
  contactInformation: 'Contact Information',
  latestCallTime: 'Latest Call Time',
  numberOfCallsToday: 'Number Of Calls Today',
  templateType: 'Template Type',
  messageType: 'Message Type',
  sendTime: 'Send Time',
  sendStatus: 'Send Status',
  transmitFrequency: 'Transmit Frequency',
  successfullySent: 'Successfully Sent',
  failInSend: 'Fail In Send',
  sendTimeSeconds: 'Send Time(Seconds)',
  templateDescription: 'Template Description',
  applicationSubmitter: 'Application Submitter',
  sender: 'Sender',
  templateName: 'Template Name',
  failureReason: 'Failure Reason',
  successfullySwitchedApplications: 'Successfully Switched Applications',
  switchedApplicationsTips: 'Confirm To Switch The Application To',
  cannotBeEmpty: 'Cannot Be Empty',
  right: 'Right',
  alreadyDeleted: 'Already Deleted',
  templateDetails: 'Template Details',
  contentExample: 'Content Example',
  example: 'Example',
  templateMessagePreview: 'Template Message Preview',
  preview: 'Preview',
  send: 'Send',
  deletePrompt: 'Are You Sure You Want To Delete It?',
  sendTemplateMessage: 'Send Template Message',
  upload: 'Upload',
  downloadTemplate: 'Download Template',
  describe: 'Describe',
  templateSearch: 'Template Search',
  cancelSend: 'Cancel Send',
  pauseSend: 'Pause Send',
  continueSend: 'Continue Send',
  sendReport: 'Send Report',
  downloadDetails: 'Download Details',
  selectApplication: 'Please Select An Application',
  pleaseUser: 'Please Select A User',
  picturePreview: 'Picture Preview',
  uploadFailedTips: 'Upload Failed, Please Try Again Later',
  uploadImgSizeTips: 'The Maximum Size Of The Image Cannot Exceed',
  applicationsEmpty: 'There Are Currently No Available Applications',
  sentTotal: 'Total Number Of Sent',
  downloadData:'Download Data',
  templateNumber :'Template Number',
  customTemplateNumber:'Custom Template Number'

};
