export default {
  addSupplier: 'Add Supplier',
  updateSuppliers: 'Update Suppliers',
  loseEfficacy: 'Lose Efficacy',
  controlManagementDescribe:
    'Central Control Management Is Used To Provide Services For Third-Party Suppliers, Supporting The Addition Of Multiple Suppliers And Allowing For Flexible Configuration Of Interface Call Permissions For Each Module Based On A Single Supplier',
  activityName: 'Activity Name',
  ipAuthenticated: 'Is The IP Authenticated',
  iPAddress: 'IP Address',
  periodOfValidity: 'Period Of Validity',
  validityDate: 'Validity Date',
  webCallback: 'Webpage Authorization Callback',
  webCallbackTips: 'The Webpage Authorization Domain Interface Must Add A Secure Domain Name To Take Effect',
  interfacePermissions: 'Interface Permissions',
  checkAll: 'Check All',
  appkeyTips: 'The APPKEY Has Been Generated. After Closing This Page, The APPKEY Will No Longer Be Displayed. Please Save It Properly.',
  appkeyTipsRed: 'I Have Learned That APPKEY Will No Longer Be Displayed And Have Copied And Saved The APPKEY',
  phoneVerification: 'Please Enter The Correct Phone Number',
  weChatOfficialAccountManagementTitle:'WeChat Official Account Central Control Management',
  miniProgramManagementTitle:'Mini Program Central Control Management'
};
