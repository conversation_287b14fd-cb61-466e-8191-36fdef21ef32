import { createI18n } from 'vue-i18n';
const ZH_CN = 'zh-CN';
const EN_US = 'en-US';
// The arguments passed to require.context must be literals!
const zhModules = require.context(`./lang/zh-CN`, true, /\.js$/);
const enModules = require.context(`./lang/en-US`, true, /\.js$/);

export const siphonI18n = (lang, prefix = ZH_CN) => {
  const langsObj = {};
  lang.keys().forEach(item => {
    let fileName = item.replace(`./${prefix}/`, '').replace(/^\.\//, '');
    fileName = fileName.substring(0, fileName.lastIndexOf('.'));
    const keyList = fileName.split('/');
    const moduleName = keyList.shift();
    const langFileModule = lang(item)?.['default'];
    langsObj[moduleName] = langFileModule;
  });
  return langsObj;
};
const messages = {
  zh: {
    ...siphonI18n(zhModules, ZH_CN)
  },
  en: {
    ...siphonI18n(enModules, EN_US)
  }
};
const getLanguage = () => {
  if (window.WXAUTH?.getLang) {
    return window.WXAUTH?.getLang();
  } else {
    return navigator.language;
  }
};

export const i18n = () => {
  const locale = getLanguage();
  return createI18n({
    legacy: false,
    locale,
    messages
  });
};

export function usI18n(app) {
  app.use(i18n());
}
