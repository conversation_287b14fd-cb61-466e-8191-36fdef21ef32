import container from './modules/container.vue';
import pagination from './modules/pagination.vue';
import searchForm from './modules/searchForm/index.vue';
import navHeader from './modules/navHeader.vue';

const globalComponents = {};
globalComponents.install = function (Vue) {
  Vue.component('container', container);
  Vue.component('pagination', pagination);
  Vue.component('searchForm', searchForm);
  Vue.component('navHeader', navHeader);
};
export default globalComponents;
