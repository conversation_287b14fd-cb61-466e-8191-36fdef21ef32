import {
  Row,
  Col,
  Button,
  Table,
  Form,
  Input,
  Select,
  DatePicker,
  Modal,
  Radio,
  Upload,
  Tabs,
  Image,
  Checkbox,
  Menu,
  Pagination,
  ConfigProvider,
  Spin,
  Progress,
  Segmented,
  Popover,
  Empty,
  message
} from 'ant-design-vue';
const getPopupContainer = () => {
  return document.getElementById('wxmsgcenterapp');
};
message.config({
  getContainer: getPopupContainer
});
const components = [Popover, Empty, Row, Col, Button, Table, Form, Input, Select, DatePicker, Modal, Upload, Tabs, Image, Checkbox, Menu, Pagination, ConfigProvider, Radio, Spin, Progress, Segmented];
export const componentsPlugin = app => components.forEach(app.use, app);
