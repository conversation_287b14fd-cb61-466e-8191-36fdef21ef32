<template>
  <div class="search-form-area-h">
    <a-form layout="inline" class="general-query-warrper" autocomplete="off">
      <template v-if="params.length">
        <a-form-item v-for="(item, i) in params" :label="item.label" :key="i">
          <template v-if="item.slotName">
            <slot :name="item.slotName" />
          </template>
          <component v-else @onChange="searchChange" :is="queryComponents[`query-${item.type}`]" :item="item" :form="state.form" />
        </a-form-item>
      </template>
      <slot name="left"></slot>
    </a-form>
    <div class="search-form-btns-area" v-if="showLeftBtns">
      <a-button @click="reset"  v-if="showReset" type="text">{{t('common.reset')}}</a-button>
      <a-button @click="clickSearch"  v-if="showQuery">{{t('common.search')}}</a-button>
      <slot name="btns"></slot>
    </div>
  </div>
</template>

<script setup>
import { onMounted, reactive } from 'vue';
import { useI18n } from 'vue-i18n';
const { t } = useI18n();
import { queryComponents } from './items/index.js';
const props = defineProps({
  query: Object,
  params: {
    required: true,
    type: Array,
    default: () => []
  },

  showLeftBtns: {
    type: Boolean,
    default: true
  },
  showQuery: {
    type: Boolean,
    default: true
  },
  showReset: {
    type: Boolean,
    default: true
  },
  isImmediatelyQuery: {
    //是否值改变立刻请求接口数据  否：手动触发-查询按钮请求接口数据
    type: Boolean,
    default: false
  },
});
const emit = defineEmits(['searchEvent']);
let state = reactive({
  form: {},
  defaultForm: {} //初始状态
});
const reset = () => {
  state.form = { ...state.defaultForm };
  emit('searchEvent', state.form, 'reset');
};
const clickSearch = () => {
  emit('searchEvent', state.form, 'search');
};
const searchChange = val => {
  state.form = { ...state.form, ...val };
  if (props.isImmediatelyQuery) {
    clickSearch();
  }
};

onMounted(() => {
  if (props.query) {
    state.defaultForm = { ...props.query }; // 保存初始状态
    state.form = { ...props.query };
  }
});
</script>

<style lang="scss" scoped>
.search-form-area-h {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  align-items: flex-end;
  padding: 15px;
  border-top: 0;
  border-bottom: 0;
  background: white;
  .search-form-btns-area {
    margin-top: 10px;
    button {
      margin-left: 10px;
    }
  }
}
.general-query-warrper {
  flex: 1;
  position: relative;
  display: flex;
  flex-wrap: wrap;
  background: #ffffff;
  .ant-form-item {
    padding-bottom: 0;
    margin-right: 20px;
  }
  .el-input,
  .el-select {
    width: 200px;
  }
  .ml-auto {
    margin-left: auto;
  }

  &.no-item-mb .a-form-item {
    margin-bottom: 0;
  }
}
</style>
