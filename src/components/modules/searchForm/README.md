#数据示例

```
 params: [
        {
          key: "keyword",
          type: "input",
          label: "输入框",
        },
        {
          label: "自定义插槽位置",//必须
          slotName: "customLocation", //可在循环体中自定义插槽
        },
        {
          key: "type", // 参数名
          type: "select", // 表单项类型
          label: "下拉选择", // 表单项名称
          options: [ // 下拉框选项
            {
              label: "实物",
              value: 1,
            },
            {
              label: "异业券",
              value: 2,
            },
            {
              label: "CRM",
              value: 3,
            },
            {
              label: "预约服务",
              value: 5,
            },
          ],
          optValue: "value", // 可选 - 下拉框取值字段名，默认 value
          optLabel: "label", // 可选 - 下拉框展示字段名，默认 label
          notFilterable: false, // 可选 - 取值为true时禁用下拉框筛选功能
          notClearable: false, // 可选 - 取值为true时禁用表单项清空功能
          defaultAll: true, // 可选 - 取值为true时将提供展示名称为“全部”，默认值为undefined的选项
        },
        {
          label: "时间组件",
          key: ["startTime", "endTime"], // 日期范围可传入数组（例如，后端接口参数的日期要出入startTime和endTime），但需要额外配置setRange为true
          dateType: 'date', //详见 https://www.antdv.com/components/date-picker-cn
        },
      ]
```
