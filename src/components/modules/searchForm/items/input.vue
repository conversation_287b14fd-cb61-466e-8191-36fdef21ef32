<template>
  <div class="general-query-item">
    <a-input
      @clear="dataChange"
      @change="dataChange"
      v-model:value="state.currForm[item.key]"
      :placeholder="t('common.pleaseEnter')"
      :allowClear="!item.notClearable"
    ></a-input>
  </div>
</template>
<script setup>
import { onMounted, reactive, watch } from 'vue';
import { useI18n } from 'vue-i18n';
const { t } = useI18n();
const props = defineProps(['form', 'item']);
const emit = defineEmits(['onChange']);
let state = reactive({
  currForm: {}
});
const dataChange = (e) => {
  emit('onChange', state.currForm);
};
watch(
  () => props.form,
  () => {
    state.currForm = props.form;
  }
);
onMounted(() => {
  if (props.form) {
    state.currForm = props.form;
  }
});
</script>
