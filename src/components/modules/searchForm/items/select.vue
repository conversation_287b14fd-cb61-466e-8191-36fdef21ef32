<template>
  <div class="general-query-item">
    <a-select
     :getPopupContainer='getPopupContainer'
      @change="handleItemChange"
      @clear="handleItemChange"
      v-model:value="state.currForm[item.key]"
      :placeholder="t('common.pleaseSelect')"
      :filterable="!item.notFilterable"
      :allowClear="!item.notClearable"
      :multiple="item.multiple"
    >
        <a-select-option v-for="(opt, index) in item.options || []" :value="opt[item.optValue || 'value']" :key="index">{{
         opt[item.optLabel || 'label']
        }}</a-select-option>
    </a-select>
  </div>
</template>
<script setup>
import { onMounted, reactive, watch } from 'vue';
import { useI18n } from 'vue-i18n';
const { t } = useI18n();
const props = defineProps(['form', 'item']);
const emit = defineEmits(['onChange']);
let state = reactive({
  currForm: {}
});
const handleItemChange = val => {
  const {
    item: { key }
  } = props;
  const _value = val === '' ? undefined : val;
  state.currForm[key] = _value;
  emit('onChange', state.currForm);
};
const getPopupContainer =()=>{
 return document.getElementById("wxmsgcenterapp")
}
watch(
  () => props.form,
  () => {
    state.currForm = props.form;
  }
);
onMounted(() => {
  if (props.form) {
    state.currForm = props.form;
  }
});
</script>
