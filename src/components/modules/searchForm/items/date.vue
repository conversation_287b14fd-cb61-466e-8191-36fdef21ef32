<template>
  <div class="general-query-item">
    <a-range-picker
      :getPopupContainer="getPopupContainer"
      @change="rangeChange"
      v-model:value="state.range"
      :picker="item.dateType || 'date'"
      value-format="YYYY-MM-DD"
      :allowClear="!item.notClearable"
    ></a-range-picker>
  </div>
</template>
<script setup>
import { reactive, watch } from 'vue';
import { useI18n } from 'vue-i18n';
const { t } = useI18n();
const props = defineProps(['form', 'item']);
const emit = defineEmits(['onChange']);
let state = reactive({
  currForm: {},
  range: []
});
watch(
  () => props.form,
  () => {
    const {
      item: { key }
    } = props;
    if (JSON.stringify(props.form) === '{}') {
      state.range = [];
      state.currForm[key] = '';
    } else if (Array.isArray(key) && key.length > 1) {
      if (!props.form[key[0]] || !props.form[key[1]]) {
        state.range = '';
      }
    } else {
      state.currForm[key] = props.form[key];
    }
  }
);
const rangeChange = val => {
  if (!val) return;
  const {
    item: { key }
  } = props;
  const [start, end] = val || [];
  state.currForm[key[0]] = start;
  state.currForm[key[1]] = end;
  emit('onChange', state.currForm);
};
const getPopupContainer = () => {
  return document.getElementById('wxmsgcenterapp');
};
</script>
