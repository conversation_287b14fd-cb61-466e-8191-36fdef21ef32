<template>
  <div v-show="count" class="pagination-area">
    <div>
      <a-select class="general-select-bg-w" :getPopupContainer="getPopupContainer" @change="onSizesChange" :value="pageSize">
        <a-select-option v-for="(opt, index) in pageSizeArr" :value="opt" :key="index">{{ opt }} {{t('common.strip')}}/{{t('common.page')}}</a-select-option>
      </a-select>
    </div>
    <div class="page-info">
      <div class="page-text">
        {{t('common.total')}}
        <span class="page-count general-theme-color">{{ count }}</span
        > {{t('common.strip')}}
      </div>
      <a-pagination :showSizeChanger="false" :current="page" :total="count" :page-size="pageSize" @change="onPageChange" @showSizeChange="onSizesChange" show-quick-jumper></a-pagination>
    </div>
  </div>
</template>
<script setup>
import { useI18n } from 'vue-i18n';
const { t } = useI18n();
const props = defineProps({
  page: {
    required: true
  },
  pageSize: {
    required: true
  },
  count: {
    required: true
  },
  pageSizeArr: {
    type: Array,
    default: () => [10, 20, 40, 60, 80, 100]
  }
});
const emit = defineEmits(['update:page', 'update:pageSize', 'current-change']);
let onPageChange = page => {
  emit('current-change', page);
};
let onSizesChange = sizes => {
  emit('update:pageSize', sizes);
  emit('current-change', 1, sizes);
};
const getPopupContainer = () => {
  return document.getElementById('wxmsgcenterapp');
};
</script>

<style lang="scss" scoped>
::v-deep.pagination-area {
  margin-top: 15px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  .ant-pagination {
    display: flex;
    .ant-pagination-options{
      margin-right:15px;
      order:-1
    }
  }
  .page-info {
    display: flex;
    align-items: center;
  }
  .page-count {
    margin: 0 4px;
    font-weight: bold;
  }
  .page-text {
    margin-right: 16px;
    font-size: 14px;
    color: #7d7d7d;
  }
}
</style>
