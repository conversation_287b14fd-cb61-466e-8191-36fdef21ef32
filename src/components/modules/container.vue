<template>
  <div class="global-wrapper-h">
    <div class="global-container-header" v-show="!noHeader">
      <slot name="header"></slot>
    </div>
    <div
      v-if="isScroll"
      :style="{ background: mainBgColor || '' }"
      class="global-container-content global-container-content-scroll"
    >
      <slot name="content"></slot>
    </div>
    <div
      v-else
      class="global-container-content global-container-content-onscroll
      "
    >
      <slot name="content"></slot>
    </div>
    <div>
      <slot name="footer"></slot>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    noHeader: {
      type: Boolean,
      default: false,
    },
    isScroll: {
      type: Boolean,
      default: true,
    },
    mainBgColor: String,
  },
};
</script>

<style lang="scss" scoped>
.global-wrapper-h {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.global-container-content {
  flex: 1;
  margin:0 20px;
}

.global-container-content-onscroll {
  overflow: hidden;
}

.global-container-content-scroll {
  overflow: auto;

}
</style>
