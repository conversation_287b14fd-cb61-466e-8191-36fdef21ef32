<template>
  <div class="general-title-wrapper">
    <div class="flex-right-left">
      <h3><ArrowLeftOutlined @click="goBack" v-show="goBack" />{{ title }}</h3>
      <div>
        <slot name="otherBtn" />
        <a-button v-if="functionBtnFn" @click="functionBtnFn" v-debounce type="primary">{{t('common.save')}}</a-button>
      </div>
    </div>
    <div v-show="otherTipsText" class="general-wrapper-tips">
      {{ otherTipsText }}
      <slot name="otherText" />
    </div>
  </div>
</template>
<script setup>
import { ArrowLeftOutlined } from '@ant-design/icons-vue';
import { useI18n } from 'vue-i18n';
const { t } = useI18n();
const props = defineProps({
  title: String,
  goBack: Function,
  functionBtnFn: Function,
  otherTipsText: String
});
</script>
