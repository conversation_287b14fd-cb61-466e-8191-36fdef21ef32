<template>
  <div>
    <div
      :style="{
        width: width + 'px',
        height: height + 'px'
      }"
      v-if="imageUrl"
      class="img-box"
    >
      <img :src="imageUrl" alt="application icon" />
      <div class="flex-right-left" style="padding: 0">
        <CloseCircleOutlined v-show="!props.disabled" @click="handleRemove(index)" />
        <ZoomInOutlined @click="handlePictureCardPreview(imageUrl)" />
      </div>
    </div>
    <a-upload
      v-else
      :style="{
        width: width + 'px',
        height: height + 'px',
        marginRight: '10px',
        border: '1px dashed #e0e3e9'
      }"
      :accept="props.accept"
      v-model:file-list="fileList"
      :name="props.uploadName"
      list-type="picture-card"
      class="uploader-area-hh"
      :show-upload-list="false"
      :action="props.uploadUrl"
      :before-upload="beforeUpload"
      @change="handleChange"
      :headers="headersParams"
    >
      <div>
        <loading-outlined v-if="loading"></loading-outlined>
        <plus-outlined v-else></plus-outlined>
        <div class="ant-upload-text">{{t('common.upload')}}</div>
      </div>
    </a-upload>
    <a-modal getContainer="#wxmsgcenterapp" :footer="null" width="600px" v-model:open="previewDialog" :title="t('common.picturePreview')" @cancel="previewDialogClose">
      <img style="width: 100%" :src="previewImageUrl" alt="Head portrait of official account" />
    </a-modal>
  </div>
</template>
<script setup>
import { ref, watch } from 'vue';
import { useI18n } from 'vue-i18n';
const { t } = useI18n();
import { ZoomInOutlined, CloseCircleOutlined } from '@ant-design/icons-vue';
import { message } from 'ant-design-vue';
const emit = defineEmits(['successImgs']);
const props = defineProps({
  disabled: Boolean,
  width: {
    type: String,
    default: '148'
  },
  height: {
    type: String,
    default: '148'
  },
  size: {
    type: Number,
    default: 2
  },
  accept: {
    type: String,
    default: 'image/*'
  },
  currImgUrl: String,
  uploadUrl: String,
  autoUpload: {
    type: Boolean,
    default: true
  },
  uploadName: {
    type: String,
    default: 'file'
  },
  customUploadContent: {
    type: Boolean,
    default: false
  },
  customEditContent: {
    type: Boolean,
    default: false
  }
});
const fileList = ref([]);
const loading = ref(false);
const imageUrl = ref('');
const previewImageUrl = ref('');
const previewDialog = ref(false);
const accessToken = sessionStorage.getItem('accessToken');
const tenantId = sessionStorage.getItem('tenantId');
const mail = sessionStorage.getItem('mail');
const headersParams = ref({
  Authorization: 'Bearer ' + accessToken,
  'X-Tenant-Id': tenantId,
  'X-CUS-Tenant-Id': tenantId,
  'X-Email': mail
});

function getBase64(img, callback) {
  const reader = new FileReader();
  reader.addEventListener('load', () => callback(reader.result));
  reader.readAsDataURL(img);
}

const handleChange = info => {
  if (!props.autoUpload) {
    getBase64(info.file, base64Url => {
      imageUrl.value = base64Url;
    });
    loading.value = false;
  } else {
    if (info.file.status === 'uploading') {
      loading.value = true;
      return;
    }
    if (info.file.status === 'done') {
      const data = info.file.response;
      imageUrl.value = data[0].ossUrl;
      loading.value = false;
      emit('successImgs', imageUrl.value);
    }
    if (info.file.status === 'error') {
      loading.value = false;
      message.error(t('common.uploadFailedTips'));
    }
  }
};

const beforeUpload = file => {
  const isLt = file.size / 1024 / 1024 <= props.size;
  if (!isLt) {
    message.error(t('common.uploadImgSizeTips') + props.size + ' M');
    return false;
  }
  // Do you want to upload now
  loading.value = true;
  return props.autoUpload;
};
const handleRemove = index => {
  imageUrl.value = '';
  emit('successImgs', imageUrl.value);
};
const handlePictureCardPreview = fileUrl => {
  previewImageUrl.value = fileUrl;
  previewDialog.value = true;
};
const previewDialogClose = () => {
  previewImageUrl.value = '';
  previewDialog.value = false;
};
watch(
  () => props.currImgUrl,
  lastUrl => {
    if (lastUrl) {
      imageUrl.value = lastUrl;
    }
  }
);
</script>
<style lang="scss" scoped>
.avatar-uploader > .ant-upload {
  width: 128px;
  height: 128px;
}
.ant-upload-select-picture-card i {
  font-size: 32px;
  color: #999;
}

.ant-upload-select-picture-card .ant-upload-text {
  margin-top: 8px;
  color: #666;
}
.img-box {
  position: relative;
  img {
    width: 100%;
    height: 100%;
  }
  .flex-right-left {
    display: none;
    position: absolute;
    background: #000000b0;
    width: 100%;
    height: 100%;
    top: 0;
    padding: 0 40px;
    box-sizing: border-box;
    span {
      color: white;
      font-size: 20px;
      padding: 10px;
      cursor: pointer;
    }
  }
  &:hover {
    .flex-right-left {
      display: flex;
      justify-content: center;
    }
  }
}
.ant-upload-wrapper.ant-upload-picture-card-wrapper .ant-upload.ant-upload-select {
  width: 100%;
  height: 100%;
  border: none;
}
</style>
