<template>
  <div class="general-layout-wrapper">
    <div class="general-layout-main-container">
      <!-- Universal global loading DOM -->
      <a-spin style="display: none" class="general-spin-area"></a-spin>
      <app-main></app-main>
      <p class="version-p">Copyright © KERING Version Number:V1.0</p>
      <!-- Switch applications -->
      <div v-if="OFFIACOUNT_ROUTER.includes(applicationList.curPath) || MINIPROGRAM_ROUTER.includes(applicationList.curPath)" class="switch-app-area">
        <div class="app-info-h">
          <img class="app-logo-h" alt="logo of the application" v-show="currApplicationInfo?.logo" :src="currApplicationInfo?.logo" />
          <span>{{ currApplicationInfo?.wechatName }}</span>
        </div>
        <span @click="onSwitchSystemApp" v-show="applicationList.curApplicationList.length > 1" class="general-link-to">{{ t('tplMsgManagement.switchApplications') }}</span>
      </div>
    </div>
    <!-- application program -->
    <a-modal
      getContainer="#wxmsgcenterapp"
      v-if="switchSystemAppVisible"
      :footer="null"
      v-model:open="switchSystemAppVisible"
      :title="t('tplMsgManagement.switchApplications')"
      width="900px"
      @cancel="handleSwitchSystemAppClose"
    >
      <h3 v-if="applicationList.curAppType == 'miniapp'" class="application-title">
        <img src="@/assets/icon/mini_icon.png" alt="Logo of Mini Program" /> <span> {{ t('common.miniProgram') }}</span>
      </h3>
      <h3 v-if="applicationList.curAppType == 'service'" class="application-title">
        <img src="@/assets/icon/watch_icon.png" alt="Official account logo" /><span>{{ t('common.weChatOfficialAccount') }}</span>
      </h3>
      <ul class="application-area">
        <li :class="currApplicationInfo?.appid == item.appid ? 'application-li-active' : ''" @click="selectSystemApp(item)" v-for="(item, index) in applicationList.curApplicationList" :key="index">
          <a-image :fallback="IMAGE_ERROR_ADDRESS" :preview="false" :src="item.logo" />
          <p>{{ item.wechatName }}</p>
        </li>
      </ul>
    </a-modal>
  </div>
</template>
<script setup>
import { computed, ref, inject } from 'vue';
import { useRoute } from 'vue-router';
import AppMain from '@/views/layout/components/appMain';
import { getValFun } from './sysApplicationInfoUtils.js';
import { IMAGE_ERROR_ADDRESS } from '@/views/pages/commonModules/enum.js';
import { message, Modal } from 'ant-design-vue';
import { useI18n } from 'vue-i18n';
const { t } = useI18n();
import { useStore } from 'vuex';
const route = useRoute();
const { commit } = useStore();
const OFFIACOUNT_ROUTER = [
  '/cus-wxmsgcenter-fe-web-admin/offiaccount/tplMsgManagement',
  '/cus-wxmsgcenter-fe-web-admin/offiaccount/controlManagement',
  '/cus-wxmsgcenter-fe-web-admin/offiaccount/pushOfMa',
  '/cus-wxmsgcenter-fe-web-admin/offiaccount/sfmc'
]; //Official account routing entry address
const MINIPROGRAM_ROUTER = [
  '/cus-wxmsgcenter-fe-web-admin/miniprogram/subscriptionMsgManagement',
  '/cus-wxmsgcenter-fe-web-admin/miniprogram/controlManagement',
  '/cus-wxmsgcenter-fe-web-admin/miniprogram/pushOfMa'
]; //Mini program routing entry address
const reload = inject('reload');
const currApplicationInfo = computed(() => {
  return getValFun.currApplicationInfo();
});
let switchSystemAppVisible = ref(false);
const handleSwitchSystemAppClose = () => {
  switchSystemAppVisible.value = false;
};
const selectSystemApp = item => {
  // Select application
  const { appid } = currApplicationInfo.value || {};
  if (item.appid !== appid) {
    Modal.confirm({
      getContainer: '#wxmsgcenterapp',
      title:t('common.prompt'),
      content: `${t('common.switchedApplicationsTips')} ${item.wechatName}？`,
      class: 'warning-prompt-box-h',
      okText: t('common.confirm'),
      cancelText:t('common.cancel'),
      onCancel: function (e) {
        Modal.destroyAll();
      },
      onOk: async function (e) {
        Modal.destroyAll();
        sessionStorage.setItem('currApplicationInfo', JSON.stringify(item));
        sessionStorage.setItem('appid', item.appid);
        commit('SET_CURR_APPLICATION_INFO', item);
        handleSwitchSystemAppClose();
        reload();
        message.success(t('common.successfullySwitchedApplications'));
      }
    });
  }
};
const applicationList = computed(() => {
  const allApplicationList = getValFun.applicationList();
  const { type } = currApplicationInfo.value;
  let curApplicationList = []; //The application corresponding to the current route
  let curAppType = ''; //Application types include service and miniapp
  let curPath = ''; // Current routing address
  if (OFFIACOUNT_ROUTER.includes(route.path)) {
    curApplicationList = allApplicationList.filter(el => el.type == 'service');
    curAppType = 'service';
    curPath = route.path;
    if (type !== curAppType) {
      switchSystemAppVisible.value = true;
    }
  } else if (MINIPROGRAM_ROUTER.includes(route.path)) {
    curApplicationList = allApplicationList.filter(el => el.type == 'miniapp');
    curAppType = 'miniapp';
    curPath = route.path;
    if (type !== curAppType) {
      switchSystemAppVisible.value = true;
    }
  }
  return {
    curApplicationList,
    curAppType,
    curPath
  };
});
const onSwitchSystemApp = () => {
  switchSystemAppVisible.value = true;
};
</script>

<style lang="scss" scoped>
.version-p {
  padding: 15px 0;
  padding-top: 20px;
  font-size: 12px;
  text-align: center;
  color: #a7a7a7;
}

.application-title {
  padding: 10px 0;
  display: flex;
  align-items: center;
  span {
    font-size: 18px;
    font-weight: bold;
  }
  img {
    width: 20px;
    margin-right: 5px;
  }
}
::v-deep.application-area {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  min-height: 100px;
  li {
    cursor: pointer;
    width: 148px;
    height: 150px;
    margin-right: 20px;
    margin-bottom: 20px;
    text-align: center;
    border: 1px solid #f0f0f0;
    padding: 15px;
    .ant-image {
      width: 100px;
      height: 100px;
      border-radius: 50%;
      overflow: hidden;
    }

    .app-type-minbg {
      background: #42c151;
    }
    .app-type-watchbg {
      background: #6f82ff;
    }
    &:hover {
      background: whitesmoke;
      border: 1px solid black;
    }
  }
  .application-li-active {
    background: whitesmoke;
    border: 1px solid black;
  }
}
.switch-app-area {
  position: absolute;
  display: flex;
  align-items: center;
  height: 45px;
  background-color: white;
  right: 34px;
  top: 25px;
  .general-link-to {
    cursor: pointer;
    margin-left: 10px;
    border-left: 1px solid #e4e3e3;
    padding-left: 10px;
  }
  .app-info-h {
    display: flex;
    align-items: center;
    span {
      font-size: 13px;
    }
    .app-logo-h {
      margin-right: 5px;
      width: 30px;
      border-radius: 50%;
      height: 30px;
      background: #f2f2f2;
    }
  }
}
</style>
