import store from '@/store';
const getValFun = {
  applicationList: () => {
    let applicationList = sessionStorage.getItem('applicationList');
    applicationList = applicationList ? JSON.parse(applicationList) : [];
    let storeApplicationList = store.state.systemApplicationInfo.applicationList;
    return storeApplicationList.length ? storeApplicationList : applicationList;
  },
  currApplicationInfo: () => {
    let currApplicationInfo = sessionStorage.getItem('currApplicationInfo');
    currApplicationInfo = currApplicationInfo ? JSON.parse(currApplicationInfo) : {};
    let storeApplicationInfo = store.state.systemApplicationInfo.currApplicationInfo;
    return storeApplicationInfo || currApplicationInfo;
  }
};

export { getValFun };
