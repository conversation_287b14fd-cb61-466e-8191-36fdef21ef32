<template>
  <container>
    <template #header>
      <navHeader :title="t('controlManagement.updateSuppliers')" :goBack="goBack" :functionBtnFn="submitData" />
    </template>
    <template #content>
      <a-form autocomplete="off" class="general-form-area" ref="formNode" :rules="formData.formItemRules" :model="formData.formItemData" >
        <a-form-item :label="t('controlManagement.activityName')" name="name">
          <a-input show-count :maxlength="20" v-model:value="formData.formItemData.name" :placeholder="t('common.pleaseEnter')"></a-input>
        </a-form-item>
        <a-form-item :label="t('common.contacts')">
          <a-input v-model:value="formData.formItemData.contacts" :placeholder="t('common.pleaseEnter')"></a-input>
        </a-form-item>
        <a-form-item :label="t('common.contactInformation')">
          <a-input @blur="mobileBlur" v-model:value="formData.formItemData.tel" :placeholder="t('common.pleaseEnter')"></a-input>
        </a-form-item>
        <a-form-item :label="t('controlManagement.ipAuthenticated')">
          <a-radio-group class="general-radio-group-h" v-model:value="formData.formItemData.checkIp">
            <a-radio-button :value="1">{{t('common.yes')}}</a-radio-button>
            <a-radio-button :value="0">{{t('common.no')}}</a-radio-button>
          </a-radio-group>
        </a-form-item>

        <a-form-item v-if="formData.formItemData.checkIp == 1" :label="t('controlManagement.iPAddress')" name="ip">
          <a-input v-model:value="formData.formItemData.ip" :placeholder="t('common.pleaseEnter')"></a-input>
        </a-form-item>
        <a-form-item :label="t('controlManagement.periodOfValidity')">
          <a-radio-group class="general-radio-group-h" v-model:value="formData.formItemData.checkExp">
            <a-radio-button :value="true">{{t('common.yes')}}</a-radio-button>
            <a-radio-button :value="false">{{t('common.no')}}</a-radio-button>
          </a-radio-group>
        </a-form-item>
        <a-form-item v-if="formData.formItemData.checkExp" :label="t('controlManagement.validityDate')" name="extAt">
          <a-date-picker :getPopupContainer="getPopupContainer" :disabledDate="disabledDate" v-model:value="formData.formItemData.extAt" value-format="YYYY-MM-DD" :placeholder="t('common.pleaseSelect')" />
        </a-form-item>
        <a-form-item :label="t('controlManagement.webCallback')">
          <ul class="domain-area">
            <li class="domain-opt">
              <span>{{t('controlManagement.webCallbackTips')}}</span>
              <a-button @click="addDomain" type="link"> + {{t('common.add')}}</a-button>
            </li>
            <li v-for="(item, index) in formData.authorizedDomainList" :key="index">
              <a-input-group compact>
                <a-input :placeholder="t('common.pleaseEnter')" v-model:value="item.url" style="width: calc(100% - 70px)" />
                <a-button class="domain-del-icon" @click="formData.authorizedDomainList.splice(index, 1)">{{t('common.delete')}}</a-button>
              </a-input-group>
            </li>
          </ul>
        </a-form-item>
        <a-form-item :label="t('controlManagement.interfacePermissions')">
          <a-checkbox @change="handleInterfacePermissionsMultipleCheckAll" v-model:checked="formData.interfacePermissionsMultipleCheckAll"> {{t('controlManagement.checkAll')}} </a-checkbox>
        </a-form-item>
        <div class="checkbox-group-area">
          <a-checkbox-group v-model:value="formData.formItemData.interfacePermissions" :options="formData.watchInterfacePermissions" />
        </div>
      </a-form>
    </template>
  </container>
  <a-modal class="tips-modal-h" :footer="null" :keyboard="false" :maskClosable="false" :closable="false" getContainer="#wxmsgcenterapp" v-model:open="formData.createSuccessTipShow" width="600px">
    <div class="tips-area">
      <div class="tips-title">
        <ExclamationCircleFilled />
        <i>{{ t('controlManagement.appkeyTips') }}</i>
      </div>
      <p class="tips-content">
        <span>APPID</span><i>{{ formData.tipsAppId }}</i>
      </p>
      <p class="tips-content">
        <span>APPKEY</span> <i class="tips-appkey">{{ formData.tipsAppkey }}</i
        ><span class="tips-copy-link" @click="copyAppkey">{{ t('common.copy') }}</span>
      </p>
      <a-checkbox class="tips-check" v-model:checked="formData.isTipsCheck">{{t('controlManagement.appkeyTipsRed')}}</a-checkbox>
      <p class="tips-btn">
        <a-button :disabled="!formData.isTipsCheck" @click="tipsPreviewClose" type="primary"> {{t('common.confirm')}} </a-button>
      </p>
    </div>
  </a-modal>
</template>
<script setup>
import { ref, reactive, onMounted, watch } from 'vue';
import { useRoute } from 'vue-router';
const { query } = useRoute();
import { useI18n } from 'vue-i18n';
const { t } = useI18n();
import { message, Modal } from 'ant-design-vue';
import { copyProperty } from '@/utils/function';
import { ExclamationCircleFilled } from '@ant-design/icons-vue';
import { updateSuppliersForm, createSuppliersForm, detail, getApis, miniUpdateSuppliersForm, miniCreateSuppliersForm, miniDetail, miniGetApis } from '@/api/modules/controlManagement.js';
let formNode = ref(null);
const formData = reactive({
  createSuccessTipShow: false,
  isTipsCheck: false,
  tipsAppkey: '',
  tipsAppId: '',
  operationType: '',
  formLoading: false,
  interfacePermissionsMultipleCheckAll: false,
  authorizedDomainList: [],
  watchInterfacePermissions: [],
  formItemData: {
    id: undefined,
    interfacePermissions: [],
    name: '',
    contacts: '',
    tel: '',
    checkIp: 0,
    ip: '',
    checkExp: true,
    extAt: ''
  },
  formItemRules: {
    name: [{ required: true, message: t('common.pleaseEnter'), trigger: 'blur' }],
    ip: [{ required: true, message: t('common.pleaseEnter'), trigger: 'blur' }],
    extAt: [{ required: true, message:t('common.pleaseSelect'), trigger: 'change' }]
  }
});
const tipsPreviewClose = () => {
  formData.createSuccessTipShow = false;
};
const copyAppkey = () => {
  navigator.clipboard.writeText(formData.tipsAppkey);
  message.success(t('common.copySuccess'));
};
const submitData = async () => {
  formData.tipsAppId = '';
  formData.tipsAppkey = '';
  if (formNode.value) {
    await formNode.value.validate();
  }
  const domainList = formData.authorizedDomainList.filter(el => el.url);
  const params = {
    ...formData.formItemData,
    interfacePermissions: undefined,
    apiList: formData.formItemData.interfacePermissions,
    domain: domainList.map(el => el.url),
    ip: formData.formItemData.checkIp ? formData.formItemData.ip : '',
    extAt: formData.formItemData.checkExp ? formData.formItemData.extAt : ''
  };
  Modal.confirm({
    getContainer: '#wxmsgcenterapp',
    title: t('common.prompt'),
    content: t('common.operationTips'),
    class: 'warning-prompt-box-h',
    okText: t('common.confirm'),
    cancelText: t('common.cancel'),
    onCancel: function (e) {
      Modal.destroyAll();
    },
    onOk: async function (e) {
      Modal.destroyAll();
      if (params.id) {
        formData.operationType === 'miniprogram' ? await miniUpdateSuppliersForm(params) : await updateSuppliersForm(params);
      } else {
        const {
          data: { appid, appkey }
        } = formData.operationType === 'miniprogram' ? await miniCreateSuppliersForm(params) : await createSuppliersForm(params);
        formData.tipsAppId = appid;
        formData.tipsAppkey = appkey;
        formData.createSuccessTipShow = true;
      }
      message.success(t('common.operationSuccessful'));
    }
  });
};
const goBack = () => {
  const path = formData.operationType === 'miniprogram' ? '/cus-wxmsgcenter-fe-web-admin/miniprogram/controlManagement' : '/cus-wxmsgcenter-fe-web-admin/offiaccount/controlManagement';
  window.WXAUTH?.replaceTo({ path }, true);
};
const handleInterfacePermissionsMultipleCheckAll = ({ target: { checked } }) => {
  if (checked) {
    formData.formItemData['interfacePermissions'] = formData.watchInterfacePermissions.map(el => el.value);
  } else {
    formData.formItemData['interfacePermissions'] = [];
  }
};
watch(
  () => [...formData.formItemData.interfacePermissions],
  lastArr => {
    if (lastArr.length !== formData.watchInterfacePermissions.length) {
      formData.interfacePermissionsMultipleCheckAll = false;
    } else {
      formData.interfacePermissionsMultipleCheckAll = true;
    }
  }
);
const getPopupContainer = () => {
  return document.getElementById('wxmsgcenterapp');
};
const mobileBlur = () => {
  if (!/^1\d{10}$/.test(formData.formItemData.tel)) {
    message.error(t('controlManagement.phoneVerification'));
    formData.formItemData.tel = '';
  }
};
const disabledDate = currentDate => {
  const DAY_TIME = 24 * 60 * 60 * 1000;
  return currentDate && new Date(currentDate.$d).getTime() < new Date().getTime() - DAY_TIME;
};
const addDomain = () => {
  formData.authorizedDomainList.push({ url: '' });
};
const getWatchInterfacePermissions = async () => {
  const { data } = formData.operationType === 'miniprogram' ? await miniGetApis() : await getApis();
  data.forEach(el => {
    el.label = el.name;
    el.value = el.id;
  });
  formData.watchInterfacePermissions = data || {};
};
const getDetail = async () => {
  if (!formData.formItemData.id) return;
  const { data } = formData.operationType === 'miniprogram' ? miniDetail(formData.formItemData.id) : await detail(formData.formItemData.id);
  formData.formItemData = copyProperty(formData.formItemData, data);
  formData.formItemData.interfacePermissions = data.apiList || [];
  formData.authorizedDomainList = data.domain.map(el => {
    return { url: el };
  });
};
onMounted(async () => {
  const { id, operationType } = query;
  formData.formItemData.id = id || undefined;
  formData.operationType = operationType;
  getWatchInterfacePermissions();
  getDetail();
});
</script>
<style>
#wxmsgcenterapp .tips-modal-h {
  top: 25%;
}
#wxmsgcenterapp .tips-modal-h .ant-modal-body {
  padding-bottom: 10px !important;
}
</style>
<style lang="scss" scoped>
.domain-area {
  border: 1px solid #dcdfe6;
  box-sizing: border-box;
  width: 400px;
  padding: 2px 10px;
  .domain-opt {
    margin-bottom: 0;
    span {
      color: #f68584;
    }
    .a-button--small {
      padding: 0;
    }
  }
  li {
    display: flex;
    align-self: center;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 10px;
    .domain-del-icon {
      cursor: pointer;
    }
    .ant-input-group-compact {
      display: inherit;
    }
  }
}
.general-link-to {
  margin-top: -15px;
}
.checkbox-group-area {
  margin-left: 189px;
  margin-top: -26px;
  padding: 22px;
  background: whitesmoke;
}

.tips-modal-h {
  .tips-area {
    padding: 30px;
    padding-bottom: 0;
  }
  .tips-check {
    margin-left: 18px;
  }
  .tips-title {
    margin: 10px 0 20px 0;
    display: flex;
    align-items: center;
    .anticon-exclamation-circle {
      margin-right: 10px;
      color: #f56c6c;
      font-size: 16px;
    }
  }
  .tips-content {
    display: flex;
    align-items: center;
    margin-bottom: 17px;
    margin-left: 18px;
    font-size: 14px;
    color: #000000;
    span {
      width: 58px;
      color: #757575;
    }
    .tips-copy-link {
      color: #0778df;
      cursor: pointer;
    }
    i {
      margin-left: 15px;
      margin-right: 15px;
    }
    .tips-appkey {
      padding: 2px 10px;
      border: 1px solid #dcdfe6;
    }
  }
  .tips-btn {
    text-align: center;
    margin-top: 20px;
    .ant-btn {
      padding: 0 20px;
    }
    .ant-btn-primary:disabled {
      background-color: #21202045;
      border-color: #a09595;
    }
  }
}
</style>
