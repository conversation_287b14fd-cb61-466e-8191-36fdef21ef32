<template>
  <container>
    <template #header>
      <navHeader :otherTipsText="t('controlManagement.controlManagementDescribe')" :title="props.title" />
    </template>
    <template #content>
      <div class="general-padding20 genelal-theme-card-bg">
        <div class="general-mb20">
          <a-button @click="updateSuppliers" type="primary"> + {{t('controlManagement.addSupplier')}}</a-button>
        </div>
        <a-table :pagination="false" :dataSource="tableState.tableList" :columns="CONTROL_MANAGEMENT">
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'operation'">
              <span class="general-link-to" @click="updateSuppliers(record)"> {{t('common.details')}} </span>
              <span class="general-link-to general-theme-warning-color" @click="delSuppliers(record.id)"> {{ t('common.delete') }} </span>
            </template>
            <template v-else-if="column.key === 'status'">
              <span>{{ record.status == 0 ? t('controlManagement.loseEfficacy'): t('common.right') }}</span>
            </template>
          </template>
        </a-table>
      </div>
    </template>
  </container>
</template>
<script setup>
import { reactive, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { useI18n } from 'vue-i18n';
const { t } = useI18n();
import { suppliersList, deleteSuppliers, miniSuppliersList, miniDeleteSuppliers } from '@/api/modules/controlManagement.js';
import { message, Modal } from 'ant-design-vue';
import { CONTROL_MANAGEMENT } from '@/views/pages/commonModules/tableColumnsConfig.js';
const props = defineProps({
  title: String,
  operationType: String //offiaccount |or| miniprogram
});
const router = useRouter();
const tableState = reactive({
  tableList: []
});
const updateSuppliers = row => {
  window.WXAUTH?.jumpTo({ path: '/cus-wxmsgcenter-fe-web-admin/controlManagement/updateSuppliersForm', query: { id: row.id || '', operationType: props.operationType } }, false);
};
const getTableList = async () => {
  const { data } = props.operationType == 'offiaccount' ? await suppliersList() : await miniSuppliersList();
  tableState.tableList = data || [];
};
const delSuppliers = id => {
  Modal.confirm({
    getContainer: '#wxmsgcenterapp',
    title: t('common.prompt'),
    content:t('common.operationTips'),
    class: 'warning-prompt-box-h',
    okText: t('common.confirm'),
    cancelText: t('common.cancel'),
    onCancel: function (e) {
      Modal.destroyAll();
    },
    onOk: async function (e) {
      Modal.destroyAll();
      props.operationType == 'offiaccount' ? await deleteSuppliers(id) : miniDeleteSuppliers();
      message.success(t('common.operationSuccessful'));
      getTableList();
    }
  });
};
onMounted(() => {
  getTableList();
});
</script>
<style lang="scss">
.form-tips {
  position: relative;
  margin-bottom: 20px;
  margin-top: 20px;
  padding: 15px;
  font-size: 13px;
  line-height: 20px;
  color: #3c5876;
  background: white;
}
</style>
