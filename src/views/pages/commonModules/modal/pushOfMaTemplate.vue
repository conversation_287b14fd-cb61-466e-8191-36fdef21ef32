<template>
  <container>
    <template #header>
      <searchForm class="general-mn" @searchEvent="searchEvent" :params="filterState.filterCriteriaList" :query="filterState.filterParams"> </searchForm>
    </template>
    <template #content>
      <div class="general-padding20 genelal-theme-card-bg">
        <div class="general-mb20 flex-right-left">
          <a-button @click="download" type="primary"> {{t('common.downloadData')}}</a-button>
        </div>
        <a-table :pagination="false" :dataSource="tableState.tableList" :columns="tableState.columns">
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'status'">
              <span> {{ MA_SEND_STATUS[record.status] ? MA_SEND_STATUS[record.status][0] : '' }} </span>
            </template>
          </template>
        </a-table>
        <pagination :page="tableState.pageInfo.pageNumber" :pageSize="tableState.pageInfo.pageSize" :count="tableState.pageInfo.count" @current-change="getPage"></pagination>
      </div>
    </template>
  </container>
</template>
<script setup>
import { onBeforeMount, reactive } from 'vue';
import { downloadFile } from '@/utils/function.js';
import { useRouter } from 'vue-router';
const router = useRouter();
import { useI18n } from 'vue-i18n';
const { t } = useI18n();
import { maSendResultList, maSendResultListDownload, miniMessageChannelList } from '@/api/modules/subscriptionMsgManagement.js';
import { offMaSendResultList, offMaSendResultListDownload, messageChannelList } from '@/api/modules/tplMsgManagement.js';
import { PUSH_OF_MA_TABLE_FN } from '@/views/pages/commonModules/tableColumnsConfig.js';
import { MA_SEND_STATUS } from '@/views/pages/commonModules/enum.js';
const props = defineProps({
  operationType: String //offiaccount |or| miniprogram
});
const tableState = reactive({
  columns: PUSH_OF_MA_TABLE_FN(props.operationType),
  tableList: [],
  pageInfo: {
    pageNumber: 1,
    pageSize: 10,
    count: 0
  }
});
const filterState = reactive({
  filterParams: {},
  filterCriteriaList: [
    {
      key: 'templateName',
      type: 'input',
      label:t('common.templateName')
    },
    {
      key: 'templateId',
      type: 'input',
      label: t('common.templateId')
    },
    {
      key: 'unionid',
      type: 'input',
      label: 'unionid'
    },
    {
      key: 'status',
      type: 'select',
      options: MA_SEND_STATUS,
      optValue: '1',
      optLabel: '0',
      label: t('common.sendStatus')
    },
    {
      label: t('common.sendTime'),
      key: ['sendTimeStart', 'sendTimeEnd'],
      type: 'date',
      setRange: true
    },
    {
      key: 'channel',
      type: 'select',
      options: [],
      label: t('common.sender')
    },
    {
      key: 'requestId',
      type: 'input',
      label: 'Request ID'
    }
  ]
});

const getPage = (index, size) => {
  tableState.pageInfo.pageNumber = index;
  if (size) {
    tableState.pageInfo.pageSize = size;
  }
  getSendResultList();
};
const searchEvent = query => {
  filterState.filterParams = query;
  getSendResultList(true);
};
const getSendResultList = async reset => {
  if (reset) {
    tableState.pageInfo.pageNumber = 1;
  }
  const startTime = ' 00:00:00',
    endTime = ' 23:59:59';
  const { sendTimeStart, sendTimeEnd } = filterState.filterParams;
  const par = {
    ...tableState.pageInfo,
    ...filterState.filterParams,
    count: undefined,
    action: 'list',
    sendTimeStart: sendTimeStart ? `${sendTimeStart}${startTime}` : '',
    sendTimeEnd: sendTimeEnd ? `${sendTimeEnd}${endTime}` : ''
  };
  const {
    data: { list, totalCount }
  } = props.operationType == 'miniprogram' ? await maSendResultList(par) : await offMaSendResultList(par);
  tableState.tableList = list || [];
  tableState.pageInfo.count = Number(totalCount);
};
const getSenderOptions = async () => {
  let senderOptions = [];
  const { data } = props.operationType == 'miniprogram' ? await miniMessageChannelList() : await messageChannelList();
  if (data.length) {
    data.forEach(str => {
      let obj = {
        label: str,
        value: str
      };
      senderOptions.push(obj);
    });
    filterState.filterCriteriaList[5].options = senderOptions;
  }
};
const download = () => {
  const startTime = ' 00:00:00',
    endTime = ' 23:59:59';
  const { templateName, sendTimeStart, sendTimeEnd, templateId, unionid, status, channel, requestId } = filterState.filterParams;
  const params = {
    templateName: templateName || '',
    sendTimeStart: sendTimeStart ? `${sendTimeStart}${startTime}` : '',
    sendTimeEnd: sendTimeEnd ? `${sendTimeEnd}${endTime}` : '',
    templateId: templateId || '',
    unionid: unionid || '',
    status: status || '',
    channel: channel || '',
    requestId: requestId || '',
  };
  let query = `?action=download&templateName=${params.templateName}&sendTimeStart=${params.sendTimeStart}&sendTimeEnd=${params.sendTimeEnd}&templateId=${params.templateId}&unionid=${params.unionid}&status=${params.status}&channel=${params.channel}&requestId=${params.requestId}`;
  const downloadUrl = props.operationType == 'miniprogram' ? maSendResultListDownload : offMaSendResultListDownload;
  const fileName = props.operationType == 'miniprogram' ? 'miniPushOfMaFile' : 'offiaccountPushOfMaFile';
  downloadFile(downloadUrl + query, fileName);
};
onBeforeMount(() => {
  getSendResultList();
  getSenderOptions();
});
</script>
