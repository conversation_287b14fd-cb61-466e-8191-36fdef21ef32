<template>
  <a-modal getContainer="#wxmsgcenterapp" v-model:open="props.updateLanguageVisible" :title="t('tplMsgManagement.editEnglish')" width="600px" @cancel="handleClose">
    <a-form autocomplete="off" class="form-warpper">
      <a-form-item required v-for="(item, index) in props.updateLanguageFormFieldList" :label="item.label || true" :key="index">
        <a-input v-model:value="item.value" />
      </a-form-item>
    </a-form>
    <template #footer>
      <div class="dialog-footer">
        <a-button @click="handleClose">{{t('common.cancel')}}</a-button>
        <a-button @click="submit" type="primary"> {{t('common.confirm')}} </a-button>
      </div>
    </template>
  </a-modal>
</template>
<script setup>
import { message, Modal } from 'ant-design-vue';
import { miniappTemplatesEdit } from '@/api/modules/subscriptionMsgManagement.js';
import { templatesEdit } from '@/api/modules/tplMsgManagement.js';
import { useI18n } from 'vue-i18n';
const { t } = useI18n();
const props = defineProps({
  updateLanguageVisible: Boolean,
  updateLanguageFormFieldList: Array,
  updateLanguageId: String | Number,
  operationType: String //offiaccount |or| miniprogram
});
const emits = defineEmits(['update:updateLanguageVisible', 'updateLanguageEnSuccess']);
const handleClose = () => {
  emits('update:updateLanguageVisible', false);
};
const submit = async () => {
  let keyArr = [];
  const verificationFailed = props.updateLanguageFormFieldList.some(el => {
    el.value = el.value.trim();
    if (!el.value) {
      message.error(el.label +` ${t('common.cannotBeEmpty')}`);
      return true;
    } else {
      let str = `${el.value}${el.key}`;
      keyArr.push(str);
    }
  });
  if (verificationFailed) return;
  const contentEn = keyArr.join('\n');
  Modal.confirm({
    getContainer: '#wxmsgcenterapp',
    title: t('common.prompt'),
    content: t('common.operationTips'),
    class: 'warning-prompt-box-h',
    okText:  t('common.confirm'),
    cancelText: t('common.cancel'),
    onCancel: function (e) {
      Modal.destroyAll();
    },
    onOk: async function (e) {
      Modal.destroyAll();
      let par = {
        contentEn,
        id: props.updateLanguageId
      };
      if (props.operationType === 'miniprogram') {
        await miniappTemplatesEdit(par);
      } else if (props.operationType === 'offiaccount') {
        await templatesEdit(par);
      }
      message.success(t('common.operationSuccessful'));
      emits('updateLanguageEnSuccess');
      handleClose();
    }
  });
};
</script>
<style lang="scss" scoped>
.form-warpper {
  padding: 0 20px;
  padding-top: 20px;
}
::v-deep.ant-form-item {
  .ant-form-item-label {
    width: 100px !important;
  }
  .ant-picker {
    width: 100%;
  }
}

.general-form-area {
  padding-top: 20px !important;
}
::v-deep.form-item-h {
  .ant-form-item-control-input-content {
    display: flex;
  }
  .item-span-tips {
    font-size: 13px;
    display: block;
    border: 1px solid #dcdfe6;
    padding: 4px 10px;
  }
  .border-right-no {
    border-right: none;
  }
  .border-left-no {
    border-left: none;
  }
  .ant-select {
    width: 265px;
  }
}

.upload-area {
  .upload-file-name {
    margin-right: 10px;
  }
  .upload-file-tips {
    color: #96989a;
  }
}
</style>
