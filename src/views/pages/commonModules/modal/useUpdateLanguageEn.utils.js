import { reactive } from 'vue';
export const useUpdateLanguageEnFn = () => {
  const updateLanguageState = reactive({
    updateLanguageFormFieldList: [],
    updateLanguageId: '',
    updateLanguageVisible: false
  });
  const updateTemplateLanguage = row => {
    // Update the English corresponding to the template fields
    updateLanguageState.updateLanguageFormFieldList = [];
    let contentList = row.content.split('\n');
    let contentEn = row.contentEn ? row.contentEn.split('\n') : '';
    contentList.forEach((item, index) => {
      if (item) {
        const curLable = item.split('{{');
        const curLableEn = contentEn && curLable.length > 1 && curLable[1] ? contentEn.filter(el => el.includes(curLable[1])) : '';
        let langeuageInfo = {
          label: curLable[0],
          value: curLableEn.length ? curLableEn[0].split('{{')[0] : '',
          key: curLable.length > 1 ? '{{' + curLable[1] : ''
        };
        updateLanguageState.updateLanguageFormFieldList.push(langeuageInfo);
      }
    });
    updateLanguageState.updateLanguageId = row.id;
    updateLanguageState.updateLanguageVisible = true;
  };

  return {
    updateTemplateLanguage,
    updateLanguageState
  };
};
