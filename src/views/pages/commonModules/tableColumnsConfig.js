import { i18n } from '@/local/index';
const { t } = i18n().global;
//General fields for header
const GENERAL_FILED_HEADER = Object.freeze({
  name: {
    dataIndex: 'name',
    key: 'name'
  },
  status: {
    title: t('common.status'),
    dataIndex: 'status',
    key: 'status'
  },
  number: {
    title: t('common.number'),
    dataIndex: 'id',
    key: 'id'
  },
  title: {
    title: t('common.templateTitle'),
    dataIndex: 'title',
    key: 'title'
  },
  operation: {
    title: t('common.operate'),
    dataIndex: 'operation',
    key: 'operation'
  },
  templateId: {
    title: t('common.templateId'),
    dataIndex: 'templateId',
    key: 'templateId'
  },
  total: {
    title: t('common.sendQuantity'),
    dataIndex: 'total',
    key: 'total'
  },
  totalCount: {
    title:t('common.sendQuantity'),
    dataIndex: 'totalCount',
    key: 'totalCount'
  },
  createdDate: {
    title: t('common.creationTime'),
    dataIndex: 'createdDate',
    key: 'createdDate'
  },
  sendTime: {
    dataIndex: 'sendTime',
    key: 'sendTime'
  },
  taskNumber: {
    title: t('common.taskNumber'),
    dataIndex: 'taskId',
    key: 'taskId'
  }
});
// Used for configuring table headers in the ControlManagement file
export const CONTROL_MANAGEMENT = [
  GENERAL_FILED_HEADER.number,
  {
    title: t('common.supplierName'),
    ...GENERAL_FILED_HEADER.name
  },
  {
    title:  t('common.contacts'),
    dataIndex: 'contacts',
    key: 'contacts'
  },
  {
    title:  t('common.contactInformation'),
    dataIndex: 'tel',
    key: 'tel'
  },
  {
    title: 'APPID',
    dataIndex: 'appid',
    key: 'appid'
  },
  {
    title: 'APPKEY',
    dataIndex: 'appkey',
    key: 'appkey'
  },
  {
    title: t('common.latestCallTime'),
    dataIndex: 'useCallTime',
    key: 'useCallTime'
  },
  {
    title: t('common.numberOfCallsToday'),
    dataIndex: 'useTimeByToday',
    key: 'useTimeByToday'
  },
  GENERAL_FILED_HEADER.status,
  GENERAL_FILED_HEADER.operation
];
// Used for configuring table headers in the sendLogs file
export const SEND_LOGS = [
  GENERAL_FILED_HEADER.number,
  {
    title:  t('common.templateTitle'),
    dataIndex: 'templateTitle',
    key: 'templateTitle'
  },
  {
    title: t('common.templateType'),
    dataIndex: 'templateType',
    key: 'templateType'
  },
  {
    title: t('common.messageType'),
    dataIndex: 'msgType',
    key: 'msgType'
  },
  GENERAL_FILED_HEADER.templateId,
  {
    title: t('common.sendTime'),
    ...GENERAL_FILED_HEADER.sendTime
  },
  GENERAL_FILED_HEADER.total,
  {
    title: t('common.sendStatus'),
    dataIndex: 'sendStatus',
    key: 'sendStatus'
  },
  GENERAL_FILED_HEADER.operation
];
// Used for configuring table headers in the templatePage file
export const TEMPLATE_PAGE_TABLE = [
  {
    ...GENERAL_FILED_HEADER.number,
    title: t('common.serialNumber')
  },
  GENERAL_FILED_HEADER.templateId,
  GENERAL_FILED_HEADER.title,
  {
    title: t('common.templateType'),
    dataIndex: 'type',
    key: 'type'
  },
  GENERAL_FILED_HEADER.status,
  {
    ...GENERAL_FILED_HEADER.createdDate,
    title: t('common.synchronizationTime')
  },
  GENERAL_FILED_HEADER.operation
];

// Used for configuring table headers in the sendRecords file
export const MINI_SEND_RECORDS_TABLE = [
  GENERAL_FILED_HEADER.taskNumber,
  {
    ...GENERAL_FILED_HEADER.title,
    title:  t('common.templateType'),
  },
  {
    title: t('common.templateTitle'),
    ...GENERAL_FILED_HEADER.name
  },
  GENERAL_FILED_HEADER.createdDate,
  {
    title: t('common.transmitFrequency'),
    dataIndex: 'minuteSendCount',
    key: 'minuteSendCount'
  },
  {
    title:  t('common.sendTime'),
    dataIndex: 'beginTime',
    key: 'beginTime'
  },
  GENERAL_FILED_HEADER.totalCount,
  {
    ...GENERAL_FILED_HEADER.status,
    title: t('common.sendStatus'),
  },
  GENERAL_FILED_HEADER.operation
];
export const MINI_SEND_REPORT_TABLE = [
  GENERAL_FILED_HEADER.totalCount,
  {
    title:  t('common.successfullySent'),
    dataIndex: 'sendCount',
    key: 'sendCount'
  },
  {
    title: t('common.failInSend'), 
    dataIndex: 'failCount',
    key: 'failCount'
  },
  {
    title:  t('common.sendTimeSeconds'),
    dataIndex: 'useTime',
    key: 'useTime'
  },
  GENERAL_FILED_HEADER.operation
];
export const SERVICE_SEND_RECORDS_TABLE = [
  GENERAL_FILED_HEADER.taskNumber,
  GENERAL_FILED_HEADER.title,
  {
    title:  t('common.templateDescription'),
    ...GENERAL_FILED_HEADER.name
  },
  GENERAL_FILED_HEADER.createdDate,
  {
    title: t('common.applicationSubmitter'),
    dataIndex: 'applicant',
    key: 'applicant'
  },
  {
    title: t('common.sendTime'), 
    dataIndex: 'sendAt',
    key: 'sendAt'
  },
  GENERAL_FILED_HEADER.total,
  {
    ...GENERAL_FILED_HEADER.status,
    title:  t('common.sendStatus')
  },
  GENERAL_FILED_HEADER.operation
];
export const SERVICE_SEND_REPORT_TABLE = [
  GENERAL_FILED_HEADER.total,
  {
    title: t('common.successfullySent') ,
    dataIndex: 'sendSuccess',
    key: 'sendSuccess'
  },
  {
    title:t('common.failInSend'),
    dataIndex: 'sendFail',
    key: 'sendFail'
  },
  {
    title: t('common.sendTimeSeconds'),
    ...GENERAL_FILED_HEADER.sendTime
  },
  {
    ...GENERAL_FILED_HEADER.status,
    title:  t('common.sendStatus')
  },
  GENERAL_FILED_HEADER.operation
];
export const SERVICE_TEMPLATE_TABLE = [
  {
    ...GENERAL_FILED_HEADER.number,
    title: t('common.serialNumber')
  },
  GENERAL_FILED_HEADER.title,
  GENERAL_FILED_HEADER.templateId,
  GENERAL_FILED_HEADER.status,
  {
    title:  t('common.synchronizationTime'),
    dataIndex: 'modifiedDate',
    key: 'modifiedDate'
  },
  GENERAL_FILED_HEADER.operation
];
let PUSH_OF_MA_TABLE = [
  {
    title: 'Request ID',
    dataIndex: 'requestId',
    key: 'requestId'
  },
  {
    title: t('common.sender'), 
    dataIndex: 'channel',
    key: 'channel'
  },
  {
    title: '',
    dataIndex: 'wechatName',
    key: 'wechatName'
  },
  {
    title: '',
    dataIndex: 'appid',
    key: 'appid'
  },
  {
    title: t('common.templateName'), 
    dataIndex: 'templateName',
    key: 'templateName'
  },
  GENERAL_FILED_HEADER.templateId,
  {
    title: '',
    dataIndex: 'openid',
    key: 'openid'
  },
  {
    title: '',
    dataIndex: 'unionid',
    key: 'unionid'
  },
  {
    title:t('common.sendTime'),  
    ...GENERAL_FILED_HEADER.sendTime
  },
  {
    ...GENERAL_FILED_HEADER.status,
    title: t('common.sendStatus'), 
  },
  {
    title: t('common.failureReason'),
    dataIndex: 'failMassage',
    key: 'failMassage'
  }
];
export const PUSH_OF_MA_TABLE_FN = operationType => {
  const title = operationType === 'offiaccount' ? t('common.weChatOfficialAccount') : t('common.miniProgram');
  PUSH_OF_MA_TABLE.forEach(el => {
    switch (el.key) {
      case 'wechatName':
        el.title = `${title} ${t('common.name')}`;
        break;
      case 'appid':
        el.title = `${title} Appid`;
        break;
      case 'openid':
        el.title = `${title} Openid`;
        break;
      case 'unionid':
        el.title = `${title} Unionid`;
        break;
    }
  });
  return PUSH_OF_MA_TABLE;
};

export const USER_ASSIGNMENT_TABLE = [
  {
    title: t('userAssignmentOffiaccount.loginName'),
    dataIndex: 'loginName',
    key: 'loginName',
    width: 230
  },
  {
    title: t('userAssignmentOffiaccount.application'),
    dataIndex: 'apploacationInfo',
    key: 'apploacationInfo'
  },
  {
    title: t('common.creationTime'),
    dataIndex: 'createdDate',
    key: 'createdDate',
    width: 170
  },
  {
    title:t('common.status'),
    dataIndex: 'isDelete',
    key: 'isDelete',
    width: 170
  },
  {
    ...GENERAL_FILED_HEADER.operation,
    width: 170
  }
];
