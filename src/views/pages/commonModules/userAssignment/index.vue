。
<template>
  <container>
    <template #header>
      <searchForm class="general-mn" @searchEvent="searchEvent" :params="filterState.filterCriteriaList" :query="filterState.filterParams">
        <template #apploacationId>
          <a-select :getPopupContainer="getPopupContainer" showSearch optionFilterProp="label" :placeholder="`${t('common.pleaseSelect')} ${t('userAssignmentOffiaccount.application')}`" class="other-select-width" v-model:value="tableState.brandsId">
            <a-select-option v-for="item in applicationList" :key="item.id" :value="item.id" :label="item.label">{{ item.label }}</a-select-option>
          </a-select>
        </template>
      </searchForm>
    </template>
    <template #content>
      <div class="general-padding20 genelal-theme-card-bg">
        <div class="general-mb20">
          <a-button type="primary" @click="addUser"> {{t('userAssignmentOffiaccount.addUser')}}</a-button>
          <a-button @click="syncUserStatus" class="general-ml20 general-mr20" type="primary">  {{t('userAssignmentOffiaccount.userStatusSynchronization')}}</a-button>
          <a-button @click="download" type="primary"> {{ props.operationType == 'offiaccount' ? t('userAssignmentOffiaccount.downloadWeChatOfficialAccountUsers') : t('userAssignmentOffiaccount.downloadMiniProgramUsers') }}</a-button>
        </div>
        <a-table id="wxmsgcenter-user-assignment-table" :pagination="false" :dataSource="tableState.tableList" :columns="USER_ASSIGNMENT_TABLE">
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'isDelete'">
              <span :style="{ color: record.isDelete ? USER_ASSIGNMENT_STATUS_OBJ[0][2] : USER_ASSIGNMENT_STATUS_OBJ[1][2] }">
                {{ record.isDelete ? USER_ASSIGNMENT_STATUS_OBJ[0][0] : USER_ASSIGNMENT_STATUS_OBJ[1][0] }}
              </span>
            </template>
            <template v-else-if="column.key === 'apploacationInfo'">
              <div class="flex-right-left">
                <p v-show="!record.apploacationMoreInfo">{{ record.minApploacationInfo }}{{ record.applyList.length > 8 ? '...' : '' }}</p>
                <p v-show="record.apploacationMoreInfo">{{ record.maxApploacationInfo }}</p>
                <DownOutlined v-show="!record.apploacationMoreInfo && record.applyList.length > 8" @click="showMoreApploacation(record)" />
                <UpOutlined v-show="record.apploacationMoreInfo && record.applyList.length > 8" @click="showMoreApploacation(record)" />
              </div>
            </template>
            <template v-else-if="column.key === 'operation'">
              <span @click="deleteUser(record)" class="general-link-to general-theme-warning-color"> {{t('common.removes')}} </span>
            </template>
          </template>
        </a-table>
        <pagination :page="tableState.pageInfo.pageNumber" :pageSize="tableState.pageInfo.pageSize" :count="tableState.pageInfo.count" @current-change="getPage"></pagination>
      </div>
    </template>
  </container>
  <updateUser
    :applicationList="applicationList"
    :operationType="props.operationType"
    @updateUserSuccess="searchEvent"
    v-if="tableState.updateUserVisible"
    v-model:updateUserVisible="tableState.updateUserVisible"
  />
</template>
<script setup>
import { onBeforeMount, reactive, computed, h } from 'vue';
import { DownOutlined, UpOutlined } from '@ant-design/icons-vue';
import { message, Modal, Select } from 'ant-design-vue';
import { useRoute } from 'vue-router';
import { downloadFile } from '@/utils/function.js';
import { getValFun } from '@/views/layout/sysApplicationInfoUtils.js';
import { featchUserInfoAuthPermission } from '@/api/modules/applicationManagement.js';
import { featchAuthorityList, syncAuthorityList, delAuthorityList, downloadAuthorityList } from '@/api/modules/tplMsgManagement.js';
import { miniFeatchAuthorityList, miniSyncAuthorityList, miniDelAuthorityList, downloadMiniAuthorityList } from '@/api/modules/subscriptionMsgManagement.js';
import updateUser from './dialog/updateUser.vue';
import { USER_ASSIGNMENT_TABLE } from '@/views/pages/commonModules/tableColumnsConfig.js';
import { USER_ASSIGNMENT_STATUS_OBJ } from '@/views/pages/commonModules/enum.js';
import { useI18n } from 'vue-i18n';
const { t } = useI18n();
import { useStore } from 'vuex';
const { dispatch } = useStore();
const route = useRoute();
const props = defineProps({
  operationType: String //offiaccount |or| miniprogram
});
const OFFIACOUNT_ROUTER = '/userAssignmentOffiaccount',
  MINIPROGRAM_ROUTER = '/userAssignmentMiniprogram';
const applicationList = computed(() => {
  const allApplicationList = getValFun.applicationList();
  if (route.path.includes(OFFIACOUNT_ROUTER)) {
    return allApplicationList.filter(item => item.type === 'service');
  } else if (route.path.includes(MINIPROGRAM_ROUTER)) {
    return allApplicationList.filter(item => item.type === 'miniapp');
  }
});
const filterState = reactive({
  filterParams: {},
  filterCriteriaList: [
    {
      key: 'loginName',
      type: 'input',
      label: t('userAssignmentOffiaccount.loginName')
    },
    {
      label: t('userAssignmentOffiaccount.application'),
      slotName: 'apploacationId'
    },
    {
      label:  t('common.creationTime'),
      key: ['createdDateStart', 'createdDateEnd'],
      type: 'date',
      setRange: true
    },
    {
      key: 'isDelete',
      type: 'select',
      options: USER_ASSIGNMENT_STATUS_OBJ,
      optValue: '1',
      optLabel: '0',
      label: t('common.status')
    }
  ]
});
const getPopupContainer = () => {
  return document.getElementById('wxmsgcenterapp');
};
const tableState = reactive({
  updateUserVisible: false,
  brandsId: undefined,
  roleList: [],
  tableList: [],
  deleteAppid: undefined,
  deleApplicationList: [],
  pageInfo: {
    pageNumber: 1,
    pageSize: 10,
    count: 20
  }
});
const syncUserStatus = async () => {
  const tenantId = sessionStorage.getItem('tenantId');
  const par = {
    pageNumber: 1,
    pageSize: 5000,
    disabled: false,
    tenantId,
    sort: 'created_date,desc'
  };
  const {
    data: { data }
  } = await featchUserInfoAuthPermission(par);
  const {
    data: { items }
  } =
    props.operationType === 'offiaccount'
      ? await featchAuthorityList({ pageNumber: 1, pageSize: 5000, isDelete: false })
      : await miniFeatchAuthorityList({ pageNumber: 1, pageSize: 5000, isDelete: false });
  let loginNameArrDiff = [];
  if (data?.length && items?.length) {
    const loginNameArr = data.map(el => el.loginName);
    const userAssignmentLoginNameArr = items.map(el => el.loginName);
    loginNameArrDiff = userAssignmentLoginNameArr.filter(item => !loginNameArr.includes(item));
  }
  if (loginNameArrDiff.length) {
    props.operationType === 'miniprogram' ? await miniSyncAuthorityList({ loginName: loginNameArrDiff.join() }) : await syncAuthorityList({ loginName: loginNameArrDiff.join() });
    getUserAssignmentList(true);
  }
  message.success(t('userAssignmentOffiaccount.userStatusSyncSuccessful'));
};
const addUser = () => {
  tableState.updateUserVisible = true;
};
const deleteUser = ({ id, isDelete, loginName, applyList }) => {
  tableState.deleApplicationList = applyList;
  const modalContentFn = () => {
    return [
      h('div', { style: 'color: #030303;font-size: 12px;margin-bottom: 10px;' }, t('tplMsgManagement.deleteUserTips')),
      h(Select, {
        options: tableState.deleApplicationList,
        style: 'width:275px',
        showSearch: true,
        optionFilterProp: 'label',
        getPopupContainer,
        onChange: value => {
          tableState.deleteAppid = value;
        },
        placeholder: t('common.pleaseSelect')
      })
    ];
  };

  Modal.confirm({
    title: t('common.prompt'),
    content: isDelete ? t('userAssignmentOffiaccount.removeUserTips') : modalContentFn,
    class: 'warning-prompt-box-h',
    getContainer: '#wxmsgcenterapp',
    okText:t('common.confirm'),
    okButtonProps: { style: { backgroundColor: '#a12117', color: 'white', boxShadow: 'none' } },
    cancelText: t('common.cancel'),
    onCancel: function () {
      tableState.deleteAppid = undefined;
      tableState.deleApplicationList = [];
    },
    onOk: function (closeModal) {
      let par = {
        loginName,
        brandsIds: isDelete ? undefined : tableState.deleteAppid
      };
      const featchSuccess = closeModal => {
        tableState.deleteAppid = undefined;
        tableState.deleApplicationList = [];
        message.success(t('common.operationSuccessful'));
        closeModal();
        getUserAssignmentList(true);
      };
      if (!isDelete && !par.brandsIds) {
        // The current status is enabled or the role has been deleted
        message.error(t('userAssignmentOffiaccount.removedApplicationsTips'));
        return;
      }
      if (props.operationType === 'offiaccount') {
        delAuthorityList(par).then(() => {
          featchSuccess(closeModal);
        });
      } else {
        miniDelAuthorityList(par).then(() => {
          featchSuccess(closeModal);
        });
      }
    }
  });
};
const getPage = (index, size) => {
  tableState.pageInfo.pageNumber = index;
  if (size) {
    tableState.pageInfo.pageSize = size;
  }
  getUserAssignmentList();
};
const searchEvent = (query, type) => {
  if (type === 'reset') {
    tableState.brandsId = undefined;
  }
  filterState.filterParams = query || {};
  getUserAssignmentList(true);
};
const showMoreApploacation = record => {
  record.apploacationMoreInfo = !record.apploacationMoreInfo;
};
const getUserAssignmentList = async reset => {
  if (reset) {
    tableState.pageInfo.pageNumber = 1;
  }
  const startTime = ' 00:00:00',
    endTime = ' 23:59:59';
  const { loginName, createdDateStart, createdDateEnd, isDelete } = filterState.filterParams;
  const { brandsId } = tableState;
  const par = {
    ...tableState.pageInfo,
    count: undefined,
    loginName,
    brandsId,
    isDelete,
    action: 'list',
    createdDateStart: createdDateStart ? `${createdDateStart}${startTime}` : '',
    createdDateEnd: createdDateEnd ? `${createdDateEnd}${endTime}` : ''
  };
  const {
    data: { items, totalCount }
  } = props.operationType === 'offiaccount' ? await featchAuthorityList(par) : await miniFeatchAuthorityList(par);
  items.forEach(item => {
    item.apploacationMoreInfo = false;
    item.maxApplocationList = [];
    item.minApploacationInfo = '';
    item.maxApploacationInfo = '';
    item.applyList.forEach((el, index) => {
      el.label = el.wechatName;
      el.value = el.brandsId;
      const delimiterStr = `${index + 1 === item.applyList.length ? '' : '、'}`;
      if (index > 7) {
        item.maxApploacationInfo += `${el.wechatName}${delimiterStr}`;
      } else {
        item.maxApploacationInfo += `${el.wechatName}${delimiterStr}`;
        item.minApploacationInfo += `${el.wechatName}${delimiterStr}`;
      }
    });
  });
  tableState.tableList = items || [];
  tableState.pageInfo.count = Number(totalCount);
};
const download = id => {
  const downUrl = props.operationType === 'offiaccount' ? downloadAuthorityList : downloadMiniAuthorityList;
  const { loginName, createdDateStart, createdDateEnd, isDelete } = filterState.filterParams;
  const { brandsId } = tableState;
  const startTime = ' 00:00:00',
    endTime = ' 23:59:59';
  const par = {
    loginName: loginName || '',
    brandsId: brandsId || '',
    isDelete: isDelete || '',
    action: 'list',
    createdDateStart: createdDateStart ? `${createdDateStart}${startTime}` : '',
    createdDateEnd: createdDateEnd ? `${createdDateEnd}${endTime}` : ''
  };
  const queryData = `?action=download&loginName=${par.loginName}&brandsId=${par.brandsId}&isDelete=${par.isDelete}&createdDateStart=${par.createdDateStart}&createdDateEnd=${par.createdDateEnd}`;
  downloadFile(downUrl + queryData, `${props.operationType == 'offiaccount' ? 'official_account' : 'miniprogram'}_user`);
};
onBeforeMount(() => {
  dispatch('getApplicationList');
  getUserAssignmentList();
});
</script>
<style lang="scss">
#wxmsgcenter-user-assignment-table {
  .ant-table-cell-ellipsis::after {
    content: 'xx';
    width: 2px;
    height: 2px;
    background-color: red;
  }
}
</style>
<style scoped>
.other-select-width {
  width: 350px;
}
.ant-table-cell-ellipsis::after {
  content: 'xx';
  width: 2px;
  height: 2px;
  background-color: red;
}
</style>
