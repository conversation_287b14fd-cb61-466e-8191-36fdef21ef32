<template>
  <a-modal getContainer="#wxmsgcenterapp" v-model:open="props.updateUserVisible" :title="t('userAssignmentOffiaccount.addUser')" width="600px" @cancel="updateUserVisibleClose">
    <div class="flex-wrap-left" id="form-user-add">
      <p><i class="preview-tips">*</i>{{ t('userAssignmentOffiaccount.addUser') }}</p>
      <a-select
        :getPopupContainer="getPopupContainer"
        show-search
        optionFilterProp="label"
        :placeholder="`${t('common.pleaseSelect')}`"
        class="other-select-w"
        v-model:value="formState.brandsIds"
      >
        <a-select-option v-for="item in props.applicationList" :key="item.id" :value="item.id" :label="item.label">{{ item.label }}</a-select-option>
      </a-select>
      <a-select :getPopupContainer="getPopupContainer" :placeholder="t('userAssignmentOffiaccount.pleaseSelectUser')" class="other-select-w" show-search optionFilterProp="label" v-model:value="formState.loginName">
        <a-select-option v-for="item in formState.userList" :key="item.loginName" :value="item.loginName" :label="item.loginName">{{ item.loginName }}</a-select-option>
      </a-select>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <a-button @click="updateUserVisibleClose">{{t('common.cancel')}}</a-button>
        <a-button @click="updateUserSubmit" type="primary"> {{t('userAssignmentOffiaccount.confirmAdd')}} </a-button>
      </div>
    </template>
  </a-modal>
</template>
<script setup>
import { reactive, onMounted } from 'vue';
import { message, Modal } from 'ant-design-vue';
import { featchUserInfoAuthPermission } from '@/api/modules/applicationManagement.js';
import { updateAuthorityList } from '@/api/modules/tplMsgManagement.js';
import { miniUpdateAuthorityList } from '@/api/modules/subscriptionMsgManagement.js';
import { useI18n } from 'vue-i18n';
const { t } = useI18n();
const getPopupContainer = () => {
  return document.getElementById('wxmsgcenterapp');
};
const props = defineProps({
  operationType: String, //offiaccount |or| miniprogram
  updateUserVisible: Boolean,
  applicationList: Array
});
const emits = defineEmits(['update:updateUserVisible', 'updateUserSuccess']);
const formState = reactive({
  brandsIds: undefined,
  loginName: undefined,
  userList: []
});
const getUserList = async () => {
  const tenantId = sessionStorage.getItem('tenantId');
  const par = {
    pageNumber: 1,
    pageSize: 5000,
    disabled: false,
    tenantId,
    sort: 'created_date,desc'
  };
  const {
    data: { data }
  } = await featchUserInfoAuthPermission(par);
  formState.userList = data || [];
};

const updateUserVisibleClose = () => {
  emits('update:updateUserVisible', false);
};
const updateUserSubmit = async () => {
  const { brandsIds, loginName } = formState;
  if (!brandsIds) {
    return message.error(t('common.selectApplication'));
  }
  if (!loginName) {
    return message.error(t('common.pleaseUser'));
  }
  Modal.confirm({
    title: t('common.prompt'),
    content: t('common.addUserTips'),
    class: 'warning-prompt-box-h',
    getContainer: '#wxmsgcenterapp',
    okText: t('common.confirm'),
    cancelText: t('common.cancel'),
    onCancel: function (e) {
      Modal.destroyAll();
    },
    onOk: async function (e) {
      Modal.destroyAll();
      props.operationType === 'offiaccount' ? await updateAuthorityList({ brandsIds, loginName }) : await miniUpdateAuthorityList({ brandsIds, loginName });
      message.success(t('common.operationSuccessful'));
      updateUserVisibleClose();
      emits('updateUserSuccess');
    }
  });
};
onMounted(() => {
  getUserList();
});
</script>
<style lang="scss" scoped>
.preview-tips {
  margin-right: 5px;
  font-size: 14px;
  color: #ff000a;
}

.other-select-w {
  margin-left: 20px;
}
</style>
