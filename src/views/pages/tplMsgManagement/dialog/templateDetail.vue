<template>
  <a-modal getContainer="#wxmsgcenterapp" :footer="null" v-model:open="props.templateDetailVisible" :title="t('common.templateDetails')" width="400px" @cancel="handleClose">
    <ul class="det-area-ul">
      <li>
        <span class="li-small">{{t('common.contentExample')}}</span>
        <p v-for="(item, index) in props.templateDetailInfo?.content" :key="index">
          {{ item }}
        </p>
      </li>
    </ul>
  </a-modal>
</template>
<script setup>
import { onMounted } from 'vue';
import { useI18n } from 'vue-i18n';
const { t } = useI18n();
const props = defineProps({
  templateDetailVisible: Boolean,
  templateDetailInfo: Object
});
const emits = defineEmits(['update:templateDetailVisible']);
const handleClose = () => {
  emits('update:templateDetailVisible', false);
};
onMounted(() => {});
</script>
<style lang="scss" scoped>
.det-area-ul {
  background: white;
  border-radius: 4px;
  padding: 0 20px;
  box-shadow: 0 0 10px #e0e0e0;
  overflow: hidden;
  li {
    padding: 15px 0;
    .area-ul-avatar {
      width: 30px;
      height: 30px;
      margin-right: 10px;
    }
    span {
      font-size: 14px;
      color: black;
    }
    .li-small {
      margin-bottom: 19px;
      font-size: 16px;
      display: block;
      font-weight: bold;
    }
    p {
      margin-top: 10px;
      line-height: 20px;
      color: #8f8b8b;
    }
    i {
      margin-left: 10px;
    }
    .tips-p {
      font-size: 13px;
      color: #929394;
      margin-top: 21px;
    }
  }
}
</style>
