<template>
  <a-modal getContainer="#wxmsgcenterapp" v-model:open="props.subscriptionMsgPreview"
    :title="t('common.templateMessagePreview')" width="500px" @cancel="subscriptionMsgPreviewClose">
    <div>
      <a-textarea :placeholder="t('tplMsgManagement.templateMessagePreviewTips')" :auto-size="{ minRows: 3 }"
        v-model:value="openid"></a-textarea>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <a-button @click="subscriptionMsgPreviewClose">{{ t('common.cancel') }}</a-button>
        <a-button @click="subscriptionMsgPreviewSubmit" type="primary">{{ t('common.confirm') }} </a-button>
      </div>
    </template>
  </a-modal>
</template>
<script setup>
import { createTemplatePlans } from '@/api/modules/tplMsgManagement.js';
import { message, Modal } from 'ant-design-vue';
import { useI18n } from 'vue-i18n';
const { t } = useI18n();
import { ref } from 'vue';
const props = defineProps({
  subscriptionMsgPreview: false,
  curPlanId: Number | String
});
const emits = defineEmits(['update:subscriptionMsgPreview']);
const openid = ref('');
const subscriptionMsgPreviewClose = () => {
  openid.value = ''
  emits('update:subscriptionMsgPreview', false);
};
const subscriptionMsgPreviewSubmit = () => {
  if (!openid.value) {
    message.error(`${t('common.pleaseEnter')} Openid`);
    return;
  }
  const params = {
    id: props.curPlanId,
    openid: openid.value,
    action: 'preview'
  };
  Modal.confirm({
    getContainer: '#wxmsgcenterapp',
    title: t('common.prompt'),
    content: t('common.operationTips'),
    class: 'warning-prompt-box-h',
    okText: t('common.confirm'),
    cancelText: t('common.cancel'),
    onCancel: function () {
      Modal.destroyAll();
    },
    onOk: async function () {
      Modal.destroyAll();
      await createTemplatePlans(params);
      subscriptionMsgPreviewClose();
      message.success(t('common.operationSuccessful'));
    }
  });
};
</script>
