<template>
  <a-modal getContainer="#wxmsgcenterapp" v-model:open="props.sendTmplateMsgVisible" :title="t('common.sendTemplateMessage')" width="600px" @cancel="handleClose">
    <a-form ref="formNode" class="form-warpper" :rules="formData.formItemRules" :model="formData.formItemData">
      <a-form-item :label="t('tplMsgManagement.operationTipstTitle')" required>
        <p>{{ t('tplMsgManagement.operationTips') }}</p>
      </a-form-item>
      <a-form-item :label="t('tplMsgManagement.uploadFiles')" name="fileName" class="upload-area">
        <a-upload :before-upload="() => false" :show-upload-list="false" @change="uploadChangeFile" action="" accept=".csv">
          <a-button type="primary">{{ t('common.upload') }}</a-button>
        </a-upload>
        <a-button @click="download">{{ t('common.downloadTemplate') }}</a-button>
        <p class="upload-file-name">
          {{ formData.formItemData.fileName }}
        </p>
        <p class="upload-file-tips">{{ t('tplMsgManagement.uploadTips') }}</p>
      </a-form-item>
      <a-form-item :label="t('common.sendTime')" name="isPlan">
        <a-select :getPopupContainer="getPopupContainer" filterable v-model:value="formData.formItemData.isPlan" :placeholder="t('common.pleaseSelect')">
          <a-select-option :key="index" v-for="(item, index) in formData.isPlanOption" :value="item.value">
            {{ item.label }}
          </a-select-option>
        </a-select>
      </a-form-item>
      <a-form-item v-if="formData.formItemData.isPlan == 1" :label="t('tplMsgManagement.plannedSendTime')" name="sendAt">
        <a-date-picker
          :getPopupContainer="getPopupContainer"
          :disabledDate="disabledDate"
          show-time
          v-model:value="formData.formItemData.sendAt"
          value-format="YYYY-MM-DD HH:mm:ss"
          :placeholder="t('common.pleaseSelect')"
        />
      </a-form-item>
    </a-form>
    <template #footer>
      <div class="dialog-footer">
        <a-button @click="handleClose">{{ t('common.cancel') }}</a-button>
        <a-button @click="submit" type="primary"> {{ t('common.confirm') }} </a-button>
      </div>
    </template>
  </a-modal>
</template>
<script setup>
import { reactive, ref } from 'vue';
import { message, Modal } from 'ant-design-vue';
import { downloadFile } from '@/utils/function.js';
import { useI18n } from 'vue-i18n';
const { t } = useI18n();
import { customTemplateMsgAction, downloadTemplate } from '@/api/modules/tplMsgManagement.js';
const props = defineProps({
  sendTmplateMsgVisible: Boolean,
  curPlanId: String | Number
});
const emits = defineEmits(['update:sendTmplateMsgVisible']);
let formNode = ref(null);
const formData = reactive({
  isPlanOption: [
    { label: t('tplMsgManagement.sendNow'), value: 0 },
    { label: t('tplMsgManagement.planSend'), value: 1 }
  ],
  sceneOption: [],
  formItemData: {
    sendType: '1',
    fileName: '',
    file: null,
    isPlan: 0,
    sendAt: ''
  },
  formItemRules: {
    isPlan: [{ required: true, message: t('common.pleaseSelect'), trigger: 'change' }],
    sendAt: [{ required: true, message: t('common.pleaseSelect'), trigger: 'change' }],
    fileName: [{ required: true, message:t('common.pleaseSelect'), trigger: 'blur' }]
  }
});
const handleClose = () => {
  emits('update:sendTmplateMsgVisible', false);
};
const getPopupContainer = () => {
  return document.getElementById('wxmsgcenterapp');
};
const disabledDate = currentDate => {
  const DAY_TIME = 24 * 60 * 60 * 1000;
  return currentDate && new Date(currentDate.$d).getTime() < new Date().getTime() - DAY_TIME;
};
const submit = async () => {
  if (formNode.value) {
    await formNode.value.validate();
  }
  const params = new FormData();
  params.append('planId', props.curPlanId);
  params.append('isPlan', formData.formItemData.isPlan);
  params.append('file', formData.formItemData.file);
  params.append('action', 'send');
  if (formData.formItemData.isPlan == 1) {
    params.append('sendAt', formData.formItemData.sendAt);
  }
  Modal.confirm({
    getContainer: '#wxmsgcenterapp',
    title: t('common.prompt'),
    content: t('common.operationTips'),
    class: 'warning-prompt-box-h',
    okText: t('common.confirm'),
    cancelText: t('common.cancel'),
    onCancel: function (e) {
      Modal.destroyAll();
    },
    onOk: async function (e) {
      Modal.destroyAll();
      await customTemplateMsgAction(params);
      message.success(t('common.operationSuccessful'));
      handleClose();
    }
  });
};
const uploadChangeFile = files => {
  formData.formItemData.file = files.file;
  formData.formItemData.fileName = files.file.name;
};
const uploadGetFile = file => {
  formData.formItemData.file = file.raw;
  formData.formItemData.fileName = file.name;
};
const download = () => {
  downloadFile(downloadTemplate, 'sendTemplateFile');
};
</script>
<style lang="scss" scoped>
.form-warpper {
  padding: 0 20px;
  padding-top: 20px;
}
::v-deep.ant-form-item {
  .ant-form-item-label {
    width: 100px !important;
  }
  .ant-picker {
    width: 100%;
  }
}

.form-item-h {
  .item-span-tips {
    font-size: 13px;
    display: block;
    border: 1px solid #dcdfe6;
    padding: 4px 10px;
  }
  .border-right-no {
    border-right: none;
  }
  .border-left-no {
    border-left: none;
  }
  .a-select {
    width: 255px;
  }
}

.upload-area {
  .upload-file-name {
    color: #405591;
  }
  .upload-file-tips {
    color: #96989a;
  }
}
</style>
