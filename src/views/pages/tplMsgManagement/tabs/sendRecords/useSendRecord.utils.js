import { reactive, onBeforeMount, onUnmounted } from 'vue';
import { message, Modal } from 'ant-design-vue';
import { i18n } from '@/local/index';
const { t } = i18n().global;
import { downloadFile } from '@/utils/function.js';
import { customTemplateMsgAction, templateTasksList, downloadError } from '@/api/modules/tplMsgManagement.js';
let timeoutEvent = null;
export const useSendRecordFn = () => {
  const filterState = reactive({
    filterParams: {},
    filterCriteriaList: [
      {
        label: t('common.creationTime'),
        key: ['searchTimeStart', 'searchTimeEnd'],
        type: 'date',
        setRange: true
      },
      {
        key: 'name',
        type: 'input',
        label: t('common.templateSearch')
      }
    ]
  });
  const tableState = reactive({
    sendRecordList: [],
    pageInfo: {
      pageNumber: 1,
      pageSize: 10,
      count: 0
    },
    sendreportVisible: false,
    sendreportList: [],
    listenSendStatusIds: {} //Storage monitoring polling sending status object
  });
  const searchEvent = query => {
    filterState.filterParams = query;
    getSendRecordList(true);
  };
  const getPage = (index, size) => {
    tableState.pageInfo.pageNumber = index;
    if (size) {
      tableState.pageInfo.pageSize = size;
    }
    getSendRecordList();
  };
  const taskInterruption = async (record, type) => {
    let action = '',
      text = '',
      status = '';
    switch (type) {
      case 'cancel':
        action = 'cancel';
        status = 0;
        text = t('common.cancelSend');
        break;
      case 'stop':
        action = 'stop';
        status = 2;
        text = t('common.pauseSend');
        break;
      case 'continue':
        action = 'continue';
        status = 1;
        text = t('common.continueSend');
        break;
    }
    Modal.confirm({
      title: t('common.prompt'),
      content: `${t('tplMsgManagement.sendRecordOperate')}'${text}'？`,
      class: 'warning-prompt-box-h',
      okText: t('common.confirm'),
      getContainer: '#wxmsgcenterapp',
      cancelText: t('common.cancel'),
      onCancel: function (e) {
        Modal.destroyAll();
      },
      onOk: async function (e) {
        Modal.destroyAll();
        const params = new FormData();
        params.append('taskIds', record.taskId);
        params.append('action', action);
        await customTemplateMsgAction(params);
        if (type == 'continue') {
          tableState.listenSendStatusIds[record.taskId] = 0;
        } else {
          delete tableState.listenSendStatusIds[record.taskId];
        }
        if (timeoutEvent) {
          clearInterval(timeoutEvent);
        }
        const taskIdsArr = Object.keys(tableState.listenSendStatusIds);
        if (taskIdsArr.length > 0) {
          timeoutEvent = setInterval(() => {
            listenSendStatus();
          }, 2000);
        }
        record.status = status;
        message.success(t('common.operationSuccessful'));
      }
    });
  };
  const sendreport = async row => {
    tableState.sendreportList = [row];
    tableState.sendreportVisible = true;
  };
  const sendreportPreviewClose = () => {
    tableState.sendreportVisible = false;
  };
  const downdetailed = id => {
    downloadFile(downloadError(id))
  };
  const listenSendStatus = async () => {
    const taskIdsArr = Object.keys(tableState.listenSendStatusIds);
    if (!taskIdsArr.length) return;
    const params = new FormData();
    params.append('taskIds', taskIdsArr.join());
    params.append('action', 'listen');
    params.append('noLoading', true);
    const { data } = await customTemplateMsgAction(params);
    let statusIds = [];
    if (JSON.stringify(data) != '{}') {
      Object.keys(data).forEach(id => {
        if (data[id] > 99) {
          statusIds.push(String(id));
          delete tableState.listenSendStatusIds[id];
        } else {
          tableState.listenSendStatusIds[id] = data[id];
        }
      });
    }
    if (statusIds.length) {
      tableState.sendRecordList.forEach(el => {
        if (statusIds.indexOf(String(el.taskId)) != -1) {
          el.status = 3;
        }
      });
    }
    const lastTaskIdsArr = Object.keys(tableState.listenSendStatusIds);
    if (!lastTaskIdsArr.length) {
      clearInterval(timeoutEvent);
    }
  };

  const getSendRecordList = async reset => {
    tableState.listenSendStatusIds = {};
    if (timeoutEvent) {
      clearInterval(timeoutEvent);
    }
    if (reset) {
      tableState.pageInfo.pageNumber = 1;
    }
    const startTime = ' 00:00:00',
      endTime = ' 23:59:59';
    const { name, searchTimeStart, searchTimeEnd } = filterState.filterParams;
    const par = {
      ...tableState.pageInfo,
      name,
      searchTimeStart: searchTimeStart ? `${searchTimeStart}${startTime}` : '',
      searchTimeEnd: searchTimeEnd ? `${searchTimeEnd}${endTime}` : '',
      count: undefined
    };
    const {
      data: { list, totalCount }
    } = await templateTasksList(par);
    tableState.sendRecordList = list || [];
    tableState.pageInfo.count = Number(totalCount);
    tableState.sendRecordList.forEach(row => {
      if (row.status == 1) {
        tableState.listenSendStatusIds[row.taskId] = 0;
      }
    });
    timeoutEvent = setInterval(() => {
      listenSendStatus();
    }, 2000);
  };
  onUnmounted(() => {
    tableState.listenSendStatusIds = {};
    if (timeoutEvent) {
      clearInterval(timeoutEvent);
    }
  });
  onBeforeMount(() => {
    getSendRecordList();
  });
  return {
    filterState,
    tableState,
    searchEvent,
    getPage,
    taskInterruption,
    sendreport,
    sendreportPreviewClose,
    downdetailed,
    listenSendStatus,
    getSendRecordList
  };
};
