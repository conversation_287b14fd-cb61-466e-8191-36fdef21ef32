<template>
  <div>
    <searchForm class="general-mb20" @searchEvent="searchEvent" :params="filterState.filterCriteriaList" :query="filterState.filterParams"> </searchForm>
    <div class="general-padding20 genelal-theme-card-bg">
      <a-table :pagination="false" :dataSource="tableState.sendRecordList" :columns="SERVICE_SEND_RECORDS_TABLE">
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'status'">
            <a-progress
              v-if="record.status == 1"
              size="small"
              class="progress-area"
              :percent="tableState.listenSendStatusIds[record.taskId] ? tableState.listenSendStatusIds[record.taskId] : 0"
            ></a-progress>
            <span v-else>
              {{ TEMPLATE_MSG_SEND_STATUS[record.status] ? TEMPLATE_MSG_SEND_STATUS[record.status][0] : '--' }}
            </span>
          </template>
          <template v-else-if="column.key === 'operation'">
            <span v-if="record.status != 3 && record.status != 0" class="general-link-to" @click="taskInterruption(record, 'cancel')"> {{t('common.cancelSend')}} </span>
            <span v-if="record.status == 1" class="general-link-to" @click="taskInterruption(record, 'stop')"> {{t('common.pauseSend')}} </span>
            <span v-if="record.status == 2" class="general-link-to" @click="taskInterruption(record, 'continue')"> {{t('common.continueSend')}} </span>
            <span v-if="record.status == 3" class="general-link-to" @click="sendreport(record)"> {{t('common.sendReport')}} </span>
          </template>
        </template>
      </a-table>
      <pagination :page="tableState.pageInfo.pageNumber" :pageSize="tableState.pageInfo.pageSize" :count="tableState.pageInfo.count" @current-change="getPage"></pagination>
    </div>
  </div>
  <a-modal getContainer="#wxmsgcenterapp" :footer="null" :title="t('common.sendReport')" v-model:open="tableState.sendreportVisible" width="1000px" @cancel="sendreportPreviewClose">
    <div class="dialog-content">
      <a-table :pagination="false" :dataSource="tableState.sendreportList" :columns="SERVICE_SEND_REPORT_TABLE">
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'status'">
            <span>
              {{ TEMPLATE_MSG_SEND_STATUS[record.status] && TEMPLATE_MSG_SEND_STATUS[record.status][0] }}
            </span>
          </template>
          <template v-else-if="column.key === 'operation'">
            <span class="general-link-to" @click="downdetailed(record.taskId)"> {{t('common.downloadDetails')}} </span>
          </template>
        </template>
      </a-table>
    </div>
  </a-modal>
</template>
<script setup>
import { TEMPLATE_MSG_SEND_STATUS } from '@/views/pages/commonModules/enum.js';
import { useI18n } from 'vue-i18n';
const { t } = useI18n();
import { useSendRecordFn } from './useSendRecord.utils.js';
import { SERVICE_SEND_RECORDS_TABLE,SERVICE_SEND_REPORT_TABLE } from '@/views/pages/commonModules/tableColumnsConfig.js';
const { filterState, tableState, searchEvent, getPage, taskInterruption, sendreport, sendreportPreviewClose, downdetailed, listenSendStatus, getSendRecordList } = useSendRecordFn();
</script>

