<template>
  <div>
    <searchForm class="general-mb20" @searchEvent="searchEvent" :params="filterState.filterCriteriaList" :query="filterState.filterParams"> </searchForm>
    <div class="general-padding20 genelal-theme-card-bg">
      <div class="general-mb20">
        <a-button @click="synchronization" type="primary"> {{ t('tplMsgManagement.synchronizeMPtemplate') }}</a-button>
      </div>
      <a-table :pagination="false" :dataSource="tableState.templateList" :columns="SERVICE_TEMPLATE_TABLE">
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'status'">
            <span>
              {{ record.status ? t('common.right') : t('common.alreadyDeleted') }}
            </span>
          </template>
          <template v-else-if="column.key === 'operation'">
            <span class="general-link-to" @click="getDetail(record)"> {{ t('common.details') }} </span>
            <span class="general-link-to" @click="useCheckTemplate(record.id)"> {{ t('tplMsgManagement.checkIfItIsAvailable') }} </span>
            <span @click="updateTemplateLanguage(record)" class="general-link-to"> {{ t('tplMsgManagement.editEnglish') }} </span>
          </template>
        </template>
      </a-table>
      <pagination :page="tableState.pageInfo.pageNumber" :pageSize="tableState.pageInfo.pageSize" :count="tableState.pageInfo.count" @current-change="getPage"></pagination>
    </div>
  </div>

  <templateDetail v-if="tableState.templateDetailVisible" v-model:templateDetailVisible="tableState.templateDetailVisible" :templateDetailInfo="tableState.templateDetailInfo" />
  <!-- Edit English -->
  <updateLanguageEn
    operationType="offiaccount"
    :updateLanguageId="updateLanguageState.updateLanguageId"
    @updateLanguageEnSuccess="getTemplateList"
    v-if="updateLanguageState.updateLanguageVisible"
    v-model:updateLanguageVisible="updateLanguageState.updateLanguageFormFieldList"
    :updateLanguageFormFieldList="updateLanguageState.updateLanguageFormFieldList"
  />
</template>
<script setup>
import { reactive, onBeforeMount } from 'vue';
import { message, Modal } from 'ant-design-vue';
import templateDetail from '../../dialog/templateDetail.vue';
import { useI18n } from 'vue-i18n';
const { t } = useI18n();
import { SERVICE_TEMPLATE_TABLE } from '@/views/pages/commonModules/tableColumnsConfig.js';
import { templatesList, checkTemplate } from '@/api/modules/tplMsgManagement.js';
import updateLanguageEn from '@/views/pages/commonModules/modal/updateLanguageEn.vue';
import { useUpdateLanguageEnFn } from '@/views/pages/commonModules/modal/useUpdateLanguageEn.utils.js';
const { updateLanguageState, updateTemplateLanguage } = useUpdateLanguageEnFn();
const filterState = reactive({
  filterParams: {
    title: ''
  },
  filterCriteriaList: [
    {
      key: 'title',
      type: 'input',
      placeholder: t('tplMsgManagement.templateSearchTips'),
      label: t('tplMsgManagement.templateSearch')
    }
  ]
});
const tableState = reactive({
  templateList: [],
  pageInfo: {
    pageNumber: 1,
    pageSize: 10,
    count: 0
  },
  templateDetailVisible: false,
  templateDetailInfo: null
});
const searchEvent = query => {
  filterState.filterParams = query;
  getTemplateList(true);
};
const getPage = (index, size) => {
  tableState.pageInfo.pageNumber = index;
  if (size) {
    tableState.pageInfo.pageSize = size;
  }
  getTemplateList();
};
const synchronization = () => {
  Modal.confirm({
    getContainer: '#wxmsgcenterapp',
    title: t('common.prompt'),
    content: t('tplMsgManagement.synchronizeMPtemplateTips'),
    class: 'warning-prompt-box-h',
    okText: t('common.confirm'),
    cancelText: t('common.cancel'),
    onCancel: function (e) {
      Modal.destroyAll();
    },
    onOk: async function (e) {
      Modal.destroyAll();
      const par = {
        action: 'sync',
        ...tableState.pageInfo,
        count: undefined
      };
      await templatesList(par);
      message.success(t('common.operationSuccessful'));
      getTemplateList(true);
    }
  });
};
const getDetail = row => {
  const templateDetailInfo = {
    content: row.content.split('\n'),
    templateId: row.templateId,
    title: row.title
  };
  tableState.templateDetailInfo = templateDetailInfo;
  tableState.templateDetailVisible = true;
};
const useCheckTemplate = id => {
  Modal.confirm({
    title: t('common.prompt'),
    getContainer: '#wxmsgcenterapp',
    content: t('tplMsgManagement.checkIfItIsAvailableTips'),
    class: 'warning-prompt-box-h',
    okText: t('common.confirm'),
    cancelText: t('common.cancel'),
    onCancel: function (e) {
      Modal.destroyAll();
    },
    onOk: async function (e) {
      Modal.destroyAll();
      await checkTemplate(id);
      message.success(t('common.operationSuccessful'));
    }
  });
};
const getTemplateList = async reset => {
  if (reset) {
    tableState.pageInfo.pageNumber = 1;
  }
  const par = {
    ...filterState.filterParams,
    ...tableState.pageInfo,
    count: undefined,
    action: 'list'
  };
  const {
    data: { list, totalCount }
  } = await templatesList(par);
  tableState.templateList = list || [];
  tableState.pageInfo.count = Number(totalCount);
};
onBeforeMount(() => {
  getTemplateList();
});
</script>

