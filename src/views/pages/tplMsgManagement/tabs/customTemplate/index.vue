<template>
  <div>
    <searchForm class="general-mb20" @searchEvent="searchEvent" :params="filterState.filterCriteriaList" :query="filterState.filterParams"> </searchForm>
    <div class="general-padding20 genelal-theme-card-bg">
      <div class="general-mb20">
        <a-button type="primary" @click="updateTemplateForm"> + {{t('tplMsgManagement.newTemplateMessage')}}</a-button>
      </div>
      <a-table :pagination="false" bordered :dataSource="tableState.newsList" :columns="tableState.columns">
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'operation'">
            <span class="general-link-to" @click="updateTemplateForm(record)"> {{t('common.view')}} </span>
            <span class="general-link-to" @click="(tableState.subscriptionMsgPreview = true), (tableState.curPlanId = record.id)"> {{t('common.preview')}} </span>
            <span class="general-link-to" @click="(tableState.sendTmplateMsgVisible = true), (tableState.curPlanId = record.id)"> {{t('common.send')}} </span>
            <span class="general-link-to" @click="lookHistoricalData(record)"> {{t('tplMsgManagement.historyData')}} </span>
            <span class="general-link-to general-theme-warning-color" @click="delateTemplate(record.id)"> {{t('common.delete')}} </span>
          </template>
        </template></a-table
      >
      <pagination :page="tableState.pageInfo.pageNumber" :pageSize="tableState.pageInfo.pageSize" :count="tableState.pageInfo.count" @current-change="getPage"></pagination>
    </div>
  </div>
  <msgPreviewTemp v-model:subscriptionMsgPreview="tableState.subscriptionMsgPreview" :curPlanId="tableState.curPlanId" />
  <sendTmplateMsg :curPlanId="tableState.curPlanId" v-if="tableState.sendTmplateMsgVisible" v-model:sendTmplateMsgVisible="tableState.sendTmplateMsgVisible" />
</template>
<script setup>
import { onBeforeMount } from 'vue';
import { useI18n } from 'vue-i18n';
const { t } = useI18n();
import sendTmplateMsg from '../../dialog/sendTmplateMsg.vue';
import msgPreviewTemp from '../../dialog/msgPreviewTemp.vue';
import { useCustomTemplateFn } from './useCustomTemplate.utils.js';
const { filterState, tableState, getTemplatePlansList, lookHistoricalData, delateTemplate, updateTemplateForm, getPage, searchEvent } = useCustomTemplateFn();
onBeforeMount(() => {
  getTemplatePlansList();
});
</script>

