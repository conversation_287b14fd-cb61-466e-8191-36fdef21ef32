<template>
  <container>
    <template #header>
      <navHeader :title="t('tplMsgManagement.historyData')" :goBack="goBack" :otherTipsText="`${t('common.templateTitle')}：`">
        <template #otherText>
          <span> {{ tableState.title }}</span>
          <span class="general-ml20 general-mr20"> {{t('common.templateId')}}：{{ tableState.templateId }} </span>
          <span> {{t('common.describe')}}：{{ tableState.name }} </span>
        </template>
      </navHeader>
    </template>
    <template #content>
      <div class="general-padding20 genelal-theme-card-bg">
        <a-table :pagination="false" :columns="tableState.columns" :dataSource="tableState.historicalList"> </a-table>
        <pagination :page="tableState.pageInfo.pageNumber" :pageSize="tableState.pageInfo.pageSize" :count="tableState.pageInfo.count" @current-change="getPage"></pagination>
      </div>
    </template>
  </container>
</template>
<script setup>
import { reactive, onMounted } from 'vue';
import { useI18n } from 'vue-i18n';
const { t } = useI18n();
import { useRoute } from 'vue-router';
const { query } = useRoute();
import { getPlanHistory } from '@/api/modules/tplMsgManagement.js';
const tableState = reactive({
  title: '',
  name: '',
  templateId: '',
  planId: '',
  columns: [
    {
      title:t('common.sendTime'),
      dataIndex: 'sendAt',
      key: 'sendAt'
    },
    {
      title: t('common.sentTotal') ,
      dataIndex: 'total',
      key: 'total'
    },
    {
      title:  t('common.successfullySent'),
      dataIndex: 'sendSuccess',
      key: 'sendSuccess'
    },
    {
      title:  t('common.failInSend'),
      dataIndex: 'sendFail',
      key: 'sendFail'
    },
    {
      title: t('common.sendTimeSeconds'),
      dataIndex: 'sendTime',
      key: 'sendTime'
    }
  ],
  historicalList: [],
  pageInfo: {
    pageNumber: 1,
    pageSize: 10,
    count: 0
  }
});
const getPage = (index, size) => {
  tableState.pageInfo.pageNumber = index;
  if (size) {
    tableState.pageInfo.pageSize = size;
  }
  getHistoryData();
};
const goBack = () => {
  window.WXAUTH?.replaceTo({ path: '/cus-wxmsgcenter-fe-web-admin/offiaccount/tplMsgManagement', query: { activeName: 'customTemplate' } }, true);
};
const getHistoryData = async () => {
  if (!tableState.planId) return;
  const par = {
    planId: tableState.planId,
    ...tableState.pageInfo,
    count: undefined
  };
  const {
    data: { list, totalCount }
  } = await getPlanHistory(par);
  tableState.historicalList = list || [];
  tableState.pageInfo.count = Number(totalCount);
};
onMounted(async () => {
  const { id, title, name, templateId } = query;
  tableState.planId = id || '';
  tableState.title = title;
  tableState.name = name;
  tableState.templateId = templateId;
  getHistoryData();
});
</script>
<style lang="scss" scoped>
.title-tips {
  position: relative;
  margin-bottom: 20px;
  padding: 15px;
  font-size: 14px;
  line-height: 20px;
  color: black;
  background: white;
}
</style>
