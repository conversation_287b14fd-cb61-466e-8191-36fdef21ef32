<template>
  <container>
    <template #header>
      <navHeader
        :otherTipsText="t('tplMsgManagement.updateTemplateMessagetTips')"
        :title="t('tplMsgManagement.updateTemplateMessage')"
        :goBack="goBack"
        :functionBtnFn="submit"
      >
        <template #otherBtn>
          <a-button @click="showTemplatePreview" v-show="formData.formItemData.planId" class="general-mr20" v-debounce type="primary" ghost>{{t('common.preview')}}</a-button>
        </template>
      </navHeader>
    </template>
    <template #content>
      <div class="flex-left-center content-bg">
        <a-form class="general-form-area" :loading="formData.formLoading" ref="formNode" :rules="formData.formItemRules" :model="formData.formItemData">
          <a-form-item :label="t('common.templateTitle')" name="templateId">
            <a-select
              :getPopupContainer="getPopupContainer"
              popupClassName="select-scroll-area"
              :disabled="formData.formItemData.planId ? true : false"
              v-model:value="formData.formItemData.templateId"
              :placeholder="t('common.pleaseSelect')"
            >
              <a-select-option v-for="item in formData.templateTypeList" :key="item.value" :value="item.id">
                {{ item.title }}
              </a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item :label="t('common.templateId')">
            <a-input disabled v-model:value="formData.formItemData.templateId" :placeholder="t('common.pleaseEnter')"></a-input>
          </a-form-item>

          <a-form-item :label="t('common.describe')">
            <a-input show-count :maxlength="30" v-model:value="formData.formItemData.name" :placeholder="t('common.pleaseEnter')"></a-input>
          </a-form-item>
          <a-form-item :label="t('tplMsgManagement.jumpAddress')">
            <a-input v-model:value="formData.formItemData.content.url" :placeholder="t('tplMsgManagement.jumpAddressPlaceholder')"></a-input>
            <p class="content-tips">
              {{ t('tplMsgManagement.jumpAddressTips') }}
            </p>
          </a-form-item>
          <a-form-item label="AppId">
            <a-input v-model:value="formData.formItemData.content.miniprogram.appid" :placeholder="t('common.pleaseEnter')"></a-input>
            <p class="content-tips">{{t('tplMsgManagement.appIdTips')}}</p>
          </a-form-item>
          <a-form-item :label="t('tplMsgManagement.miniProgramJumpAddress')">
            <a-input v-model:value="formData.formItemData.content.miniprogram.pagepath" :placeholder="t('common.pleaseEnter')"></a-input>
            <p class="content-tips">{{ t('tplMsgManagement.miniProgramJumpAddressTips') }}</p>
          </a-form-item>
          <div v-for="(item, index) in formData.curContentInfo?.list" :key="index">
            <a-form-item :label="item.label || true">
              <a-input v-model:value="item.value" :placeholder="`${t('common.pleaseEnter')} ${item.label}`"></a-input>
              <p v-show="item.tips" class="content-tips">
                {{ item.tips }}
              </p>
            </a-form-item>
          </div>
        </a-form>
        <div class="example-area">
          <ul class="det-area-ul">
            <li>
              <span class="li-small">{{t('common.example')}}</span>
              <p v-for="(item, index) in formData.curContentInfo?.example" :key="index">
                {{ item }}
              </p>
            </li>
          </ul>
          <p v-show="!formData.curContentInfo?.contentEn"><InfoCircleOutlined />{{t('tplMsgManagement.templateEnglishTips')}}</p>
        </div>
      </div>
    </template>
  </container>
  <msgPreviewTemp v-model:subscriptionMsgPreview="formData.subscriptionMsgPreview" :curPlanId="formData.formItemData.planId" />
</template>
<script setup>
import { ref, reactive, onMounted, watch, nextTick } from 'vue';
import { useRoute } from 'vue-router';
const { query } = useRoute();
import { useI18n } from 'vue-i18n';
const { t } = useI18n();
import msgPreviewTemp from '../../dialog/msgPreviewTemp.vue';
import { SUB_MSG_TEMPLATE_FIEID_TIPS } from '@/views/pages/commonModules/enum.js';
import { message, Modal } from 'ant-design-vue';
import { InfoCircleOutlined } from '@ant-design/icons-vue';
import { copyProperty } from '@/utils/function';
import { createTemplatePlans, templatesList, detailTemplatePlans, templatePlansEdit } from '@/api/modules/tplMsgManagement.js';
let formNode = ref(null);
const formData = reactive({
  formLoading: false,
  subscriptionMsgPreview: false,
  formItemData: {
    planId: '',
    templateId: '',
    name: '',
    content: {
      url: '',
      miniprogram: {
        appid: '',
        pagepath: ''
      }
    }
  },
  curContentInfo: {
    list: [],
    id: '',
    contentTitle: '',
    example: [],
    contentEn: '/'
  },
  formItemRules: {
    templateId: [{ required: true, message: t('common.pleaseSelect'), trigger: 'change' }]
  },
  templateTypeList: []
});
const submit = async () => {
  if (formNode.value) {
    await formNode.value.validate();
  }
  const params = {
    action: 'create',
    ...formData.formItemData,
    content: {
      ...formData.formItemData.content,
      data: {}
    },
    id: formData.formItemData.planId,
    planId: undefined
  };
  const contentInfo = formData.curContentInfo;
  if (contentInfo?.list) {
    contentInfo.list.forEach(el => {
      params.content.data[el.key] = { value: el.value };
    });
  }
  params.content = JSON.stringify(params.content);
  Modal.confirm({
    getContainer: '#wxmsgcenterapp',
    title:t('common.prompt'),
    content: t('common.operationTips'),
    class: 'warning-prompt-box-h',
    okText: t('common.confirm'),
    cancelText: t('common.cancel'),

    onCancel: function () {
      Modal.destroyAll();
    },
    onOk: async function () {
      Modal.destroyAll();
      if (params.id) {
        await templatePlansEdit(params);
      } else {
        const { data } = await createTemplatePlans(params);
        formData.formItemData.planId = data;
      }
      message.success(t('common.operationSuccessful'));
    }
  });
};
const handleTemplateContent = contentArr => {
  let templateTypeList = [];
  contentArr.forEach(item => {
    let templateInfo = {
      list: [],
      id: String(item.id),
      contentTitle: item.title,
      example: item.example ? item.example.split('\n') : [],
      contentEn: item.contentEn
    };
    item.content.split('\n').forEach(contentSplit => {
      if (contentSplit) {
        const labelArr = contentSplit.split('{{');
        const keyStr = labelArr.length > 1 ? labelArr[1].split('}}')[0] : '';
        let templateObj = {
          label: labelArr.length > 1 ? labelArr[0] : '',
          key: keyStr.slice(0, -5),
          value: '',
          tips: ''
        };
        Object.keys(SUB_MSG_TEMPLATE_FIEID_TIPS).forEach(key => {
          if (templateObj.key.indexOf(key) != -1) {
            templateObj.tips = SUB_MSG_TEMPLATE_FIEID_TIPS[key];
          }
        });
        templateInfo.list.push(templateObj);
      }
    });
    item.templateInfo = templateInfo;
    templateTypeList.push(item);
  });
  return templateTypeList;
};
const showTemplatePreview = () => {
  formData.subscriptionMsgPreview = true;
};
const getTemplateList = async () => {
  const {
    data: { list }
  } = await templatesList({ pageNumber: 1, pageSize: 9999, action: 'list' });
  if (list.length) {
    const resTemplateContent = handleTemplateContent(list);
    formData.formItemData.templateId = formData.formItemData.planId ? '' : list[0].id;
    formData.templateTypeList = resTemplateContent;
  }
};
const getDetialInfo = async () => {
  if (!formData.formItemData.planId) return;
  const {
    data: { customTemplate }
  } = await detailTemplatePlans(formData.formItemData.planId);
  const lastCustomTemplate = {
    ...customTemplate,
    content: JSON.parse(customTemplate.content),
    templateId: String(customTemplate.templateId)
  };
  formData.formItemData = copyProperty(formData.formItemData, lastCustomTemplate);
  //  Process the dynamic template form section
  const { data } = lastCustomTemplate.content;
  if (data && JSON.stringify(data) != '{}') {
    nextTick(() => {
      if (formData.curContentInfo?.list.length) {
        formData.curContentInfo.list.forEach(el => {
          el.value = data[el.key].value;
        });
      }
    });
  }
};
const getPopupContainer = () => {
  return document.getElementById('wxmsgcenterapp');
};
const goBack = () => {
  window.WXAUTH?.replaceTo({ path: '/cus-wxmsgcenter-fe-web-admin/offiaccount/tplMsgManagement', query: { activeName: 'customTemplate' } }, true);
};
watch(
  () => formData.formItemData.templateId,
  () => {
    if (formData.formItemData.templateId) {
      const { templateInfo } = formData.templateTypeList.find(el => el.id == formData.formItemData.templateId) || {};
      formData.curContentInfo = templateInfo ? JSON.parse(JSON.stringify(templateInfo)) : null;
    } else {
      formData.curContentInfo = null;
    }
  }
);
onMounted(async () => {
  const { id } = query;
  formData.formItemData.planId = id || undefined;
  await getTemplateList();
  getDetialInfo();
});
</script>
<style lang="scss" scoped>
.content-bg {
  background: white;
  align-items: baseline;
}
.example-area {
  margin-left: 60px;
  p {
    span {
      margin-right: 8px;
    }
    margin-top: 20px;
    font-size: 13px;
    color: red;
  }
}
.det-area-ul {
  width: 350px;
  background: white;
  border-radius: 4px;
  padding: 0 20px;
  box-shadow: 0 0 10px #e0e0e0;
  overflow: hidden;
  li {
    padding: 15px 0;
    .li-small {
      margin-bottom: 19px;
      font-size: 16px;
      display: block;
      font-weight: bold;
    }
    p {
      margin-top: 10px;
      line-height: 24px;
      color: #514a4a;
      font-size: 14px;
    }
  }
}
.content-tips {
  flex-basis: content;
  width: 400px;
  line-height: 19px;
  font-size: 13px;
  color: #afafaf;
}
::v-deep .a-form-item__content {
  display: block;
}
</style>
