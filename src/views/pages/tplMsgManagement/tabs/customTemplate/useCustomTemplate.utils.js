import { reactive } from 'vue';
import { message, Modal } from 'ant-design-vue';
import { i18n } from '@/local/index';
const { t } = i18n().global;
import { customTemplates,  templatePlansDel } from '@/api/modules/tplMsgManagement.js';
export const useCustomTemplateFn = () => {
  const filterState = reactive({
    filterParams: {
      name: '',
      searchTimeStart: '',
      searchTimeEnd: ''
    },
    filterCriteriaList: [
      {
        label: t('common.creationTime'),
        key: ['searchTimeStart', 'searchTimeEnd'],
        type: 'date',
        setRange: true
      },
      {
        key: 'name',
        type: 'input',
        label:  t('tplMsgManagement.templateSearch'),
        placeholder: t('tplMsgManagement.templateSearchTips')
      }
    ]
  });
  const tableState = reactive({
    newsList: [],
    columns: [
      {
        title: t('common.serialNumber'),
        dataIndex: 'id',
        key: 'id'
      },
      {
        title:  t('common.templateTitle'),
        dataIndex: 'title',
        key: 'title'
      },
      {
        title: t('common.templateDescription'),
        dataIndex: 'name',
        key: 'name'
      },
      {
        title:t('common.creationTime'), 
        dataIndex: 'createdDate',
        key: 'createdDate'
      },
      {
        title:t('common.operate') ,
        dataIndex: 'operation',
        key: 'operation'
      }
    ],
    pageInfo: {
      pageNumber: 1,
      pageSize: 10,
      count: 0
    },
    subscriptionMsgPreview: false,
    curPlanId: '',
    sendTmplateMsgVisible: false
  });
  const searchEvent = query => {
    filterState.filterParams = query;
    getTemplatePlansList(true);
  };
  const getPage = (index, size) => {
    tableState.pageInfo.pageNumber = index;
    if (size) {
      tableState.pageInfo.pageSize = size;
    }
    getTemplatePlansList();
  };
  const updateTemplateForm = async row => {
    window.WXAUTH?.jumpTo({ path:'/cus-wxmsgcenter-fe-web-admin/offiaccount/tplMsgManagement/newTemplateForm',  query: { id: row.id || '' } }, false);
  };

  const delateTemplate = async id => {
    Modal.confirm({
      title: t('common.prompt') ,
      getContainer: '#wxmsgcenterapp',
      content:t('common.deletePrompt'),
      class: 'warning-prompt-box-h',
      okText: t('common.confirm') ,
      cancelText:t('common.cancel') ,
      onCancel: function () {
        Modal.destroyAll();
      },
      onOk: async function () {
        await templatePlansDel(id);
        message.success(t('common.operationSuccessful'));
        getTemplatePlansList();
      }
    });
  };
  const lookHistoricalData = ({ id, title, name, templateId }) => {
    window.WXAUTH?.jumpTo({ path:'/cus-wxmsgcenter-fe-web-admin/offiaccount/tplMsgManagement/historicalData', query: { id, title, name, templateId }}, false);
  };
  const getTemplatePlansList = async reset => {
    if (reset) {
      tableState.pageInfo.pageNumber = 1;
    }
    const startTime = ' 00:00:00',
      endTime = ' 23:59:59';
    const { name, searchTimeStart, searchTimeEnd } = filterState.filterParams;
    const par = {
      ...tableState.pageInfo,
      name,
      searchTimeStart: searchTimeStart ? `${searchTimeStart}${startTime}` : '',
      searchTimeEnd: searchTimeEnd ? `${searchTimeEnd}${endTime}` : '',
      count: undefined
    };
    const {
      data: { list, totalCount }
    } = await customTemplates(par);
    tableState.newsList = list || [];
    tableState.pageInfo.count = Number(totalCount);
  };
  return {
    filterState,
    tableState,
    getTemplatePlansList,
    lookHistoricalData,
    delateTemplate,
    updateTemplateForm,
    getPage,
    searchEvent
  };
};
