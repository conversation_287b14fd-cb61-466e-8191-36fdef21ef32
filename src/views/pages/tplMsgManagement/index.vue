<template>
  <container>
    <template #header>
      <navHeader :title="t('tplMsgManagement.wechatOfficialAccountTemplateManagement')" />
      <a-tabs v-model:activeKey="activeName">
        <a-tab-pane :tab="t('tplMsgManagement.weChatMPTemplate')" key="watchTemplate"></a-tab-pane>
        <a-tab-pane :tab="t('common.customTemplate')" key="customTemplate"></a-tab-pane>
        <a-tab-pane :tab="t('common.sendRecords')" key="sendRecord"></a-tab-pane>
      </a-tabs>
    </template>
    <template #content>
      <customTemplate v-if="activeName === 'customTemplate'" />
      <watchTemplate v-if="activeName === 'watchTemplate'" />
      <sendRecords v-if="activeName === 'sendRecord'" />
    </template>
  </container>
</template>
<script setup>
import { ref, onBeforeMount } from 'vue';
import { useI18n } from 'vue-i18n';
const { t } = useI18n();
import { useRoute } from 'vue-router';
const route = useRoute();
import watchTemplate from './tabs/watchTemplate/index.vue';
import customTemplate from './tabs/customTemplate/index.vue';
import sendRecords from './tabs/sendRecords/index.vue';
let activeName = ref('watchTemplate');
onBeforeMount(() => {
  activeName.value = route.query.activeName || 'watchTemplate';
});
</script>
