<template>
  <container>
    <template #header>
      <navHeader :title="t('tplMsgManagement.sfmcWechatOfficialAccountManagement')" />
      <searchForm class="general-mb20 general-ml20 general-mr20" @searchEvent="searchEvent" :params="filterState.filterCriteriaList" :query="filterState.filterParams"> </searchForm>
    </template>
    <template #content>
      <div class="general-padding20 genelal-theme-card-bg">
        <a-table :pagination="false" :dataSource="tableState.tableList" :columns="SEND_LOGS">
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'operation'">
              <span class="general-link-to" @click="download(record)"> {{t('common.downloadDetails')}} </span>
            </template>
          </template>
        </a-table>
        <pagination :page="tableState.pageInfo.pageNumber" :pageSize="tableState.pageInfo.pageSize" :count="tableState.pageInfo.count" @current-change="getPage"></pagination>
      </div>
    </template>
  </container>
</template>
<script setup>
import { onBeforeMount, reactive } from 'vue';
import { downloadFile } from '@/utils/function.js';
import { useRouter } from 'vue-router';
const router = useRouter();
import { useI18n } from 'vue-i18n';
const { t } = useI18n();
import { recordList, downloadApi } from '@/api/modules/sendLogs.js';
import { SEND_LOGS } from '@/views/pages/commonModules/tableColumnsConfig.js';
const filterState = reactive({
  filterParams: {},
  filterCriteriaList: [
    {
      label: t('common.sendTime'),
      key: ['searchTimeStart', 'searchTimeEnd'],
      type: 'date',
      setRange: true
    },
    {
      key: 'name',
      type: 'input',
      label: t('common.templateTitle')
    }
  ]
});
const tableState = reactive({
  tableList: [],
  pageInfo: {
    pageNumber: 1,
    pageSize: 10,
    count: 0
  }
});
const getPage = (index, size) => {
  tableState.pageInfo.pageNumber = index;
  if (size) {
    tableState.pageInfo.pageSize = size;
  }
  getSendLogs();
};
const searchEvent = query => {
  filterState.filterParams = query;
  getSendLogs(true);
};
const getSendLogs = async reset => {
  if (reset) {
    tableState.pageInfo.pageNumber = 1;
  }
  const startTime = ' 00:00:00',
    endTime = ' 23:59:59';
  const { name, searchTimeStart, searchTimeEnd } = filterState.filterParams;
  const par = {
    ...tableState.pageInfo,
    count: undefined,
    name,
    action: 'list',
    searchTimeStart: searchTimeStart ? `${searchTimeStart}${startTime}` : '',
    searchTimeEnd: searchTimeEnd ? `${searchTimeEnd}${endTime}` : ''
  };
  const {
    data: { items, totalCount }
  } = await recordList(par);
  tableState.tableList = items || [];
  tableState.pageInfo.count = Number(totalCount);
};
const download = ({ id, sendTime }) => {
  downloadFile(downloadApi + `?action=download&id=${id}&sendTime=${sendTime}`);
};
onBeforeMount(() => {
  getSendLogs();
});
</script>
