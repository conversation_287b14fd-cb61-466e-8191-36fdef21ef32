<template>
  <div>
    <searchForm class="general-mb20" @searchEvent="searchEvent" :params="filterState.filterCriteriaList" :query="filterState.filterParams"> </searchForm>
    <div class="general-padding20 genelal-theme-card-bg">
      <div class="general-mb20">
        <a-button @click="synchronization" type="primary"> {{t('tplMsgManagement.synchronizeMPtemplate')}}</a-button>
      </div>
      <a-table :dataSource="tableState.templateList" :columns="TEMPLATE_PAGE_TABLE" :pagination="false">
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'status'">
            <span>
              {{ record.status == 1 ? t('common.right') : t('common.alreadyDeleted') }}
            </span>
          </template>
          <template v-else-if="column.key === 'type'">
            <span> {{ SEND_MSG_TEMPLATE_TYPE[record.type] ? SEND_MSG_TEMPLATE_TYPE[record.type][0] : '' }} </span>
          </template>
          <template v-else-if="column.key === 'operation'">
            <span class="general-link-to" @click="getDetail(record)"> {{t('common.details')}} </span>
            <span class="general-link-to" @click="checkTemplate(record.id)"> {{t('tplMsgManagement.checkIfItIsAvailable')}} </span>
            <span class="general-link-to" @click="gosubscribe(record.id)"> {{t('tplMsgManagement.subscriptionData')}} </span>
            <span @click="updateTemplateLanguage(record)" class="general-link-to"> {{t('tplMsgManagement.editEnglish')}} </span>
          </template>
        </template>
      </a-table>
      <pagination :page="tableState.pageInfo.pageNumber" :pageSize="tableState.pageInfo.pageSize" :count="tableState.pageInfo.count" @current-change="getPage"></pagination>
    </div>
  </div>
  <templateDetail v-if="tableState.timplateDetailVisible" v-model:timplateDetailVisible="tableState.timplateDetailVisible" :templateDetailInfo="tableState.templateDetailInfo" />
  <updateLanguageEn
    operationType="miniprogram"
    :updateLanguageId="updateLanguageState.updateLanguageId"
    @updateLanguageEnSuccess="getMiniappTemplatesList"
    v-if="updateLanguageState.updateLanguageVisible"
    v-model:updateLanguageVisible="updateLanguageState.updateLanguageFormFieldList"
    :updateLanguageFormFieldList="updateLanguageState.updateLanguageFormFieldList"
  />
</template>
<script setup>
import { onBeforeMount } from 'vue';
import { useI18n } from 'vue-i18n';
const { t } = useI18n();
import templateDetail from '../../dialog/templateDetail.vue';
import { SEND_MSG_TEMPLATE_TYPE } from '@/views/pages/commonModules/enum.js';
import updateLanguageEn from '@/views/pages/commonModules/modal/updateLanguageEn.vue';
import { useTemplatePageFn } from './useTemplatePage.utils.js';
import { TEMPLATE_PAGE_TABLE } from '@/views/pages/commonModules/tableColumnsConfig.js';
import { useUpdateLanguageEnFn } from '@/views/pages/commonModules/modal/useUpdateLanguageEn.utils.js';
const { updateLanguageState, updateTemplateLanguage } = useUpdateLanguageEnFn();
const { getMiniappTemplatesList, filterState, tableState, searchEvent, synchronization, getDetail, checkTemplate, gosubscribe, getPage } = useTemplatePageFn();
onBeforeMount(() => {
  getMiniappTemplatesList();
});
</script>

