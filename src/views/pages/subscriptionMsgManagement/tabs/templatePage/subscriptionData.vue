<template>
  <container>
    <template #header>
      <navHeader :title="t('tplMsgManagement.subscriptionData')" :goBack="goBack" />
      <searchForm class="general-mn search-area-tp0" @searchEvent="searchEvent" :params="filterState.filterCriteriaList" :query="filterState.filterParams"> </searchForm>
    </template>
    <template #content>
      <div class="general-padding20 genelal-theme-card-bg">
        <div class="general-mb20 flex-right-left">
          <div>
            <a-button @click="downloadSubscriptionData" type="primary"> {{t('tplMsgManagement.downloadSubscriptionData')}}</a-button>
            <a-button class="general-ml0" @click="downloadSubscriptionData({ subscribeType: 'reject' })" type="primary" ghost>{{t('tplMsgManagement.downloadUnsubscriptionData')}}</a-button>
          </div>
          <span class="total-tips">{{t('tplMsgManagement.totalNumberPeople')}}：{{ tableState.totalNumber }}</span>
        </div>
        <a-table :dataSource="tableState.tableList" :columns="tableState.columns" :pagination="false">
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'isUse'">
              <span>
                {{ SUBSCRIPTION_MSG_USE_STATUS[record.isUse] ? SUBSCRIPTION_MSG_USE_STATUS[record.isUse][0] : '' }}
              </span>
            </template>
            <template v-else-if="column.key === 'subscribeType'">
              <span>
                {{ SUBSCRIPTION_MSG_EVENT[record.subscribeType] ? SUBSCRIPTION_MSG_EVENT[record.subscribeType][0] : '' }}
              </span>
            </template>
          </template>
        </a-table>
        <pagination :showSizes="true" :page="tableState.pageInfo.pageNumber" :pageSize="tableState.pageInfo.pageSize" :count="tableState.pageInfo.count" @current-change="getPage"></pagination>
      </div>
    </template>
  </container>
</template>
<script setup>
import { onMounted, reactive } from 'vue';
import { useRoute } from 'vue-router';
const { query } = useRoute();
import { useI18n } from 'vue-i18n';
const { t } = useI18n();
import { message } from 'ant-design-vue';
import { downloadFile } from '@/utils/function.js';
import { miniappTemplatesUserList, miniappTemplatesDownload } from '@/api/modules/subscriptionMsgManagement.js';
import { SUBSCRIPTION_MSG_EVENT, SUBSCRIPTION_MSG_USE_STATUS } from '@/views/pages/commonModules/enum.js';
const filterState = reactive({
  filterParams: {},
  filterCriteriaList: [
    {
      key: 'subscribeType',
      type: 'select',
      label: t('tplMsgManagement.subscribeEvents'),
      options: SUBSCRIPTION_MSG_EVENT,
      optValue: '1',
      optLabel: '0'
    },
    {
      label: t('tplMsgManagement.subscriptionTime'),
      key: ['subscribeTimeStart', 'subscribeTimeEnd'],
      type: 'date',
      setRange: true
    },
    {
      key: 'isUse',
      type: 'select',
      label: t('common.status'),
      options: SUBSCRIPTION_MSG_USE_STATUS,
      optValue: '1',
      optLabel: '0'
    },
    {
      key: 'isDeduplication',
      type: 'select',
      label: t('tplMsgManagement.dataDeduplication'),
      options: [
        { value: 1, label: t('common.yes') },
        { value: 0, label:  t('common.no')}
      ]
    }
  ]
});
const tableState = reactive({
  moniappTemplateId: '',
  columns: [
    {
      title:t('common.serialNumber'),
      dataIndex: 'id',
      key: 'id'
    },
    {
      title: 'OpenId',
      dataIndex: 'openid',
      key: 'openid'
    },
    {
      title:t('tplMsgManagement.subscriptionScenario'),
      dataIndex: 'subscribeType',
      key: 'subscribeType'
    },

    {
      title:t('tplMsgManagement.subscriptionTime'),
      dataIndex: 'subscribeTime',
      key: 'subscribeTime'
    },
    {
      title: t('common.status'),
      dataIndex: 'isUse',
      key: 'isUse'
    }
  ],
  totalNumber: 0,
  tableList: [],
  pageInfo: {
    pageNumber: 1,
    pageSize: 10,
    count: 0
  }
});
const searchEvent = query => {
  filterState.filterParams = query;
  getMiniappTemplatesUserList(true);
};
const getPage = (index, size) => {
  tableState.pageInfo.pageNumber = index;
  if (size) {
    tableState.pageInfo.pageSize = size;
  }
  getMiniappTemplatesUserList();
};
const downloadSubscriptionData = ({ subscribeType }) => {
  const startTime = ' 00:00:00',
    endTime = ' 23:59:59';
  const lastSubscribeType = subscribeType || filterState.filterParams.subscribeType;
  const { subscribeTimeStart, subscribeTimeEnd, isUse, isDeduplication } = filterState.filterParams;
  const params = {
    subscribeTimeStart: subscribeTimeStart ? `${subscribeTimeStart}${startTime}` : '',
    subscribeTimeEnd: subscribeTimeEnd ? `${subscribeTimeEnd}${endTime}` : '',
    subscribeType: lastSubscribeType || '',
    isUse: isUse || '',
    pageNumber: tableState.pageInfo.pageNumber,
    isDeduplication: isDeduplication || 0,
    pageSize: tableState.pageInfo.pageSize
  };
  let query = `?isDeduplication=${params.isDeduplication}&isUse=${params.isUse}&pageNumber=${params.pageNumber}&pageSize=${params.pageSize}&subscribeType=${params.subscribeType}&subscribeTimeStart=${params.subscribeTimeStart}&subscribeTimeEnd=${params.subscribeTimeEnd}`;
  const fileUrl = miniappTemplatesDownload({ id: tableState.moniappTemplateId, query });
  downloadFile(fileUrl, 'miniProgramSubscriptionData');
};
const getMiniappTemplatesUserList = async reset => {
  if (!tableState.moniappTemplateId) {
    return message.error(t('tplMsgManagement.templateIdDoesNotExist'));
  }
  if (reset) {
    tableState.pageInfo.pageNumber = 1;
  }
  const startTime = ' 00:00:00',
    endTime = ' 23:59:59';
  const { subscribeType, subscribeTimeStart, subscribeTimeEnd, isUse } = filterState.filterParams;
  const par = {
    moniappTemplateId: tableState.moniappTemplateId,
    ...filterState.filterParams,
    ...tableState.pageInfo,
    count: undefined,
    subscribeType,
    subscribeTimeStart: subscribeTimeStart ? `${subscribeTimeStart}${startTime}` : '',
    subscribeTimeEnd: subscribeTimeEnd ? `${subscribeTimeEnd}${endTime}` : '',
    isUse
  };
  const {
    data: { list, totalCount }
  } = await miniappTemplatesUserList(par);
  const totalNumber = await miniappTemplatesUserList({ ...par, isDeduplication: 1 });
  tableState.totalNumber = totalNumber.data?.totalCount;
  tableState.tableList = list || [];
  tableState.pageInfo.count = Number(totalCount);
};
const goBack = () => {
  window.WXAUTH?.replaceTo({ path: '/cus-wxmsgcenter-fe-web-admin/miniprogram/subscriptionMsgManagement', query: { activeName: 'template' } }, true);
};
onMounted(() => {
  const { id } = query;
  tableState.moniappTemplateId = id || '';
  getMiniappTemplatesUserList();
});
</script>
<style lang="scss" scoped>
.search-area-tp0 {
  margin-top: 0 !important;
}
.dialog-content {
  min-height: 50vh;
  background: #f2f2f2;
  padding: 10px;
  .search-form-border {
    border-bottom: 1px solid #e9ecef;
    margin-top: -20px;
    border-style: dashed;
  }
  .general-mb20 {
    border: none;
    margin-bottom: 0;
  }
}
.total-tips {
  font-size: 12px;
}
</style>
