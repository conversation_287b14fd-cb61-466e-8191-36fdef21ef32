import { reactive } from 'vue';
import { message, Modal } from 'ant-design-vue';
import { miniappTemplatesList, miniappTemplatesCheckTemplate } from '@/api/modules/subscriptionMsgManagement.js';
import { i18n } from '@/local/index';
const { t } = i18n().global;
export const useTemplatePageFn = () => {
  const filterState = reactive({
    filterParams: {
      title: ''
    },
    filterCriteriaList: [
      {
        key: 'title',
        type: 'input',
        label: t('common.templateSearch')
      }
    ]
  });
  const tableState = reactive({
    templateList: [],
    pageInfo: {
      pageNumber: 1,
      pageSize: 10,
      count: 0
    },
    timplateDetailVisible: false,
    templateDetailInfo: null
  });
  const searchEvent = query => {
    filterState.filterParams = query;
    getMiniappTemplatesList(true);
  };
  const getPage = (index, size) => {
    tableState.pageInfo.pageNumber = index;
    if (size) {
      tableState.pageInfo.pageSize = size;
    }
    getMiniappTemplatesList();
  };
  const synchronization = () => {
    Modal.confirm({
      title: t('common.prompt'),
      content: t('tplMsgManagement.synchronizeMPtemplateTips'),
      class: 'warning-prompt-box-h',
      getContainer: '#wxmsgcenterapp',
      okText:  t('common.confirm'),
      cancelText:  t('common.cancel'),
      onCancel: function () {
        Modal.destroyAll();
      },
      onOk: async function () {
        Modal.destroyAll();
        const par = {
          action: 'sync',
          ...tableState.pageInfo,
          count: undefined
        };
        await miniappTemplatesList(par);
        message.success( t('common.operationSuccessful'));
        getMiniappTemplatesList(true);
      }
    });
  };
  const getDetail = row => {
    const templateDetailInfo = {
      example: row.example.split('\n'), 
      content: row.content.split('\n'), 
      templateId: row.templateId,
      title: row.title
    };
    tableState.templateDetailInfo = templateDetailInfo;
    tableState.timplateDetailVisible = true;
  };
  const checkTemplate = async id => {
    Modal.confirm({
      title:t('common.prompt'),
      content: t('tplMsgManagement.checkIfItIsAvailableTips'),
      class: 'warning-prompt-box-h',
      getContainer: '#wxmsgcenterapp',
      okText:  t('common.confirm'),
      cancelText:  t('common.cancel'),
      onCancel: function (e) {
        Modal.destroyAll();
      },
      onOk: async function (e) {
        Modal.destroyAll();
        await miniappTemplatesCheckTemplate(id);
        message.success( t('common.operationSuccessful'));
      }
    });
  };
  const gosubscribe = id => {
    window.WXAUTH?.jumpTo({ path: '/cus-wxmsgcenter-fe-web-admin/miniprogram/subscriptionMsgManagement/subscriptionData', query: { id } }, false);
  };
  const getMiniappTemplatesList = async reset => {
    if (reset) {
      tableState.pageInfo.pageNumber = 1;
    }
    const par = {
      action: 'list',
      ...filterState.filterParams,
      ...tableState.pageInfo,
      count: undefined
    };
    const {
      data: { list, totalCount }
    } = await miniappTemplatesList(par);
    tableState.templateList = list || [];
    tableState.pageInfo.count = Number(totalCount);
  };

  return {
    filterState,
    tableState,
    getMiniappTemplatesList,
    searchEvent,
    synchronization,
    getDetail,
    checkTemplate,
    gosubscribe,
    getPage
  };
};
