<template>
  <container>
    <template #header>
      <navHeader :title="t('tplMsgManagement.updateSubscriptionTemplate')" :goBack="goBack" :functionBtnFn="submit">
        <template #otherBtn>
          <a-button @click="showTemplatePreview" v-show="formData.formItemData.planId" class="general-mr20" v-debounce type="primary" ghost>{{t('common.preview')}}</a-button>
        </template>
      </navHeader>
    </template>
    <template #content>
      <div class="flex-left-center content-bg">
        <a-form autocomplete="off" class="general-form-area" ref="formNode" :rules="formData.formItemRules" :model="formData.formItemData">
          <a-form-item :label="t('common.templateType')" name="templateId">
            <a-select :getPopupContainer="getPopupContainer" :disabled="formData.formItemData.planId ? true : false" v-model:value="formData.formItemData.templateId" :placeholder="t('common.pleaseSelect')">
              <a-select-option v-for="item in formData.miniTemplateTypeList" :key="item.value" :value="item.id">
                {{ item.title }}
              </a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item :label="t('common.templateName')">
            <a-input show-count :maxlength="30" v-model:value="formData.formItemData.name" :placeholder="t('common.pleaseEnter')"></a-input>
          </a-form-item>
          <a-form-item :label="t('tplMsgManagement.jumpMiniProgram')">
            <a-segmented
              v-model:value="formData.formItemData.content.jumptype"
              :options="[
                { label: t('common.yes'), value: 1 },
                { label: t('common.no'), value: 0 }
              ]"
            />
          </a-form-item>
          <a-form-item :label="t('tplMsgManagement.miniProgramPath')" v-if="formData.formItemData.content.jumptype == 1">
            <a-input v-model:value="formData.formItemData.content.page" :placeholder="t('common.pleaseEnter')"></a-input>
          </a-form-item>
          <div v-for="(item, index) in formData.curMiniContentInfo?.list" :key="index">
            <a-form-item :label="item.label || true">
              <a-input v-model:value="item.value" :placeholder="`${t('common.pleaseEnter')}${item.label}`"></a-input>
              <p v-show="item.tips" class="content-tips">
                {{ item.tips }}
              </p>
            </a-form-item>
          </div>
        </a-form>
        <div class="example-area">
          <ul class="example-area-ul">
            <li class="flex-left-center">
              <a-image :preview="false" :fallback="IMAGE_ERROR_ADDRESS" class="area-ul-avatar" :src="currApplicationInfo?.logo" />
              <span>{{ currApplicationInfo?.label }}</span>
            </li>
            <li>
              <span class="li-small">{{ formData.curMiniContentInfo?.contentTitle }}</span>
              <p v-for="(item, index) in formData.curMiniContentInfo?.example" :key="index">{{ item }}</p>
            </li>
            <li v-show="formData.formItemData.content.jumptype == 1">
              <span>{{t('tplMsgManagement.lookMiniProgram')}}</span>
            </li>
            <li>
              <span>{{t('tplMsgManagement.refusalNotice')}}</span>
            </li>
          </ul>
          <p v-show="!formData.curMiniContentInfo?.contentEn"><InfoCircleOutlined />{{ t('tplMsgManagement.templateEnglishTips') }}</p>
        </div>
      </div>
    </template>
  </container>
  <subscriptionMsgPreview :curPlanId="formData.formItemData.planId" v-model:subscriptionMsgPreview="formData.subscriptionMsgPreview" />
</template>
<script setup>
import { ref, reactive, onMounted, watch, nextTick, computed } from 'vue';
import { useRoute } from 'vue-router';
const { query } = useRoute();
import subscriptionMsgPreview from '../../dialog/subscriptionMsgPreview.vue';
import { InfoCircleOutlined } from '@ant-design/icons-vue';
import { message, Modal } from 'ant-design-vue';
import { useI18n } from 'vue-i18n';
const { t } = useI18n();
import { SUB_MSG_TEMPLATE_FIEID_TIPS, IMAGE_ERROR_ADDRESS } from '@/views/pages/commonModules/enum.js';
import { copyProperty } from '@/utils/function';
import { createCustomTemplate, miniappCustomTemplatesDetail, miniappCustomTemplatesEdit, miniappTemplatesList } from '@/api/modules/subscriptionMsgManagement.js';
import { getValFun } from '@/views/layout/sysApplicationInfoUtils.js';
const currApplicationInfo = computed(() => getValFun.currApplicationInfo());
let formNode = ref(null);
const formData = reactive({
  formLoading: false,
  subscriptionMsgPreview: false,
  formItemData: {
    planId: '',
    templateId: '',
    name: '',
    content: {
      jumptype: 0,
      page: ''
    }
  },
  curMiniContentInfo: null,
  formItemRules: {
    templateId: [{ required: true, message: t('common.pleaseSelect'), trigger: 'change' }]
  },
  miniTemplateTypeList: []
});
const submit = async () => {
  if (formNode.value) {
    await formNode.value.validate();
  }
  const params = {
    action: formData.formItemData.planId ? 'edit' : 'create',
    ...formData.formItemData,
    content: {
      ...formData.formItemData.content,
      data: {}
    },
    id: formData.formItemData.planId || undefined,
    planId: undefined
  };
  const contentInfo = formData.curMiniContentInfo;
  if (contentInfo && contentInfo.list) {
    contentInfo.list.forEach(el => {
      params.content.data[el.key] = { value: el.value };
    });
  }
  params.content.page = params.content.jumptype == 0 ? '' : params.content.page;
  params.content = JSON.stringify(params.content);
  Modal.confirm({
    title:  t('common.prompt'),
    content:  t('common.operationTips'),
    class: 'warning-prompt-box-h',
    getContainer: '#wxmsgcenterapp',
    okText:  t('common.confirm'),
    cancelText: t('common.cancel'),
    onCancel: function () {
      Modal.destroyAll();
    },
    onOk: async function () {
      Modal.destroyAll();
      if (params.id) {
        await miniappCustomTemplatesEdit(params);
      } else {
        const { data } = await createCustomTemplate(params);
        formData.formItemData.planId = data;
      }
      message.success(t('common.operationSuccessful'));
    }
  });
};
const handleMiniTplContent = miniContentArr => {
  let miniTemplateTypeList = [];
  miniContentArr.forEach(item => {
    let miniTemplateInfo = {
      list: [],
      id: item.id,
      contentTitle: item.title,
      example: item.example ? item.example.split('\n') : [],
      contentEn: item.contentEn
    };
    item.content.split('\n').forEach(miniContentSplit => {
      if (miniContentSplit) {
        const titleArr = miniContentSplit.split('{{');
        const titleKeyStr = titleArr.length > 1 ? titleArr[1].split('}}')[0] : '';
        let templateObj = {
          label: titleArr.length > 1 ? titleArr[0] : '',
          key: titleKeyStr.slice(0, -5),
          value: '',
          tips: ''
        };
        Object.keys(SUB_MSG_TEMPLATE_FIEID_TIPS).forEach(key => {
          if (templateObj.key.indexOf(key) != -1) {
            templateObj.tips = SUB_MSG_TEMPLATE_FIEID_TIPS[key];
          }
        });
        miniTemplateInfo.list.push(templateObj);
      }
    });
    item.miniTemplateInfo = miniTemplateInfo;
    miniTemplateTypeList.push(item);
  });
  return miniTemplateTypeList;
};
const getMiniTemplateList = async () => {
  const {
    data: { list }
  } = await miniappTemplatesList({ pageNumber: 1, pageSize: 9999, action: 'list' });
  if (list.length) {
    const miniTemplateContent = handleMiniTplContent(list);
    formData.formItemData.templateId = formData.formItemData.planId ? '' : list[0].id;
    formData.miniTemplateTypeList = miniTemplateContent;
  }
};
const getPopupContainer = () => {
  return document.getElementById('wxmsgcenterapp');
};
const getMiniDetialInfo = async () => {
  if (!formData.formItemData.planId) return;
  const {
    data: { customTemplate }
  } = await miniappCustomTemplatesDetail(formData.formItemData.planId);
  const lastInfo = {
    ...customTemplate,
    content: JSON.parse(customTemplate.content)
  };
  formData.formItemData = copyProperty(formData.formItemData, lastInfo);
  const { data } = lastInfo.content;
  if (data && JSON.stringify(data) != '{}') {
    nextTick(() => {
      if (formData.curMiniContentInfo?.list.length) {
        formData.curMiniContentInfo.list.forEach(ele => {
          ele.value = data[ele.key].value;
        });
      }
    });
  }
};
const showTemplatePreview = () => {
  formData.subscriptionMsgPreview = true;
};
const goBack = () => {
  window.WXAUTH?.replaceTo({ path: '/cus-wxmsgcenter-fe-web-admin/miniprogram/subscriptionMsgManagement', query: { activeName: 'news' } }, true);
};
watch(
  () => formData.formItemData.templateId,
  () => {
    if (formData.formItemData.templateId) {
      const { miniTemplateInfo } = formData.miniTemplateTypeList.find(el => el.id == formData.formItemData.templateId) || {};
      formData.curMiniContentInfo = miniTemplateInfo ? JSON.parse(JSON.stringify(miniTemplateInfo)) : null;
    } else {
      formData.curMiniContentInfo = null;
    }
  }
);
onMounted(async () => {
  const { id } = query;
  formData.formItemData.planId = id || '';
  await getMiniTemplateList();
  getMiniDetialInfo();
});
</script>
<style lang="scss" scoped>
.content-bg {
  background: white;
  align-items: baseline;
}
.example-area {
  margin-left: 60px;
  p {
    span {
      margin-right: 8px;
    }
    margin-top: 20px;
    font-size: 13px;
    color: red;
  }
}
::v-deep.example-area-ul {
  width: 350px;
  background: white;
  border-radius: 4px;
  padding: 0 20px;
  box-shadow: 0 0 10px #e0e0e0;
  overflow: hidden;

  li {
    padding: 15px 0;
    border-bottom: 1px solid #ededed;
    .area-ul-avatar {
      width: 40px;
      height: 40px;
      margin-right: 10px;
    }
    span {
      font-size: 14px;
      color: black;
    }
    .li-small {
      margin-bottom: 19px;
      font-size: 16px;
      display: block;
      font-weight: bold;
    }
    p {
      margin-top: 10px;
      line-height: 20px;
      color: #8f8b8b;
      font-size: 14px;
    }
    i {
      margin-left: 10px;
    }
    .tips-p {
      font-size: 13px;
      color: #929394;
      margin-top: 21px;
    }
    &:last-child {
      border-bottom: none;
    }
  }
  &:first-child {
    margin-right: 40px;
  }
}
.content-tips {
  flex-basis: content;
  width: 400px;
  line-height: 19px;
  font-size: 13px;
  color: #afafaf;
}
::v-deep .a-form-item__content {
  display: block;
}
</style>
