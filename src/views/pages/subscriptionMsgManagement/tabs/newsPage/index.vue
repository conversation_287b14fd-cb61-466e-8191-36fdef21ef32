<template>
  <div>
    <searchForm class="general-mb20" @searchEvent="searchEvent" :params="filterState.filterCriteriaList" :query="filterState.filterParams"> </searchForm>
    <div class="general-padding20 genelal-theme-card-bg">
      <div class="general-mb20">
        <a-button type="primary" @click="updateMiniTemplateForm"> + {{t('tplMsgManagement.createSubscriptionTemplate')}}</a-button>
      </div>
      <a-table :pagination="false" :dataSource="tableState.newsList" :columns="tableState.columns">
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'templates.title'">
            <span>
              {{ record.templates?.title }}
            </span>
          </template>
          <template v-else-if="column.key === 'operation'">
            <span class="general-link-to" @click="checkDetail(record)"> {{t('common.view')}} </span>
            <span class="general-link-to" @click="updateMiniTemplateForm(record)"> {{t('common.edit')}} </span>
            <span class="general-link-to" @click="miniCustomTemplatesPreview(record.id)"> {{t('common.preview')}} </span>
            <span class="general-link-to" @click="(tableState.sendSubscriptionMsgVisible = true), (tableState.curPlanId = record.id)"> {{t('common.send')}} </span>
            <span class="general-link-to" @click="lookSendRecords(record.id)"> {{t('common.sendRecords')}} </span>
            <span class="general-link-to general-theme-warning-color" @click="delateMiniTemplate(record.id)"> {{t('common.delete')}} </span>
          </template>
        </template>
      </a-table>
      <pagination :page="tableState.pageInfo.pageNumber" :pageSize="tableState.pageInfo.pageSize" :count="tableState.pageInfo.count" @current-change="getPage"></pagination>
    </div>
  </div>
  <templateDetail v-if="tableState.timplateDetailVisible" v-model:timplateDetailVisible="tableState.timplateDetailVisible" :templateDetailInfo="tableState.templateDetailInfo" />
  <subscriptionMsgPreview  :curPlanId="tableState.curPlanId" v-model:subscriptionMsgPreview='tableState.subscriptionMsgPreview' />
  <sendSubscriptionMsg :curPlanId="tableState.curPlanId" v-if="tableState.sendSubscriptionMsgVisible" v-model:sendSubscriptionMsgVisible="tableState.sendSubscriptionMsgVisible" />
</template>
<script setup>
import { onBeforeMount } from 'vue';
import templateDetail from '../../dialog/templateDetail.vue';
import subscriptionMsgPreview from '../../dialog/subscriptionMsgPreview.vue';
import sendSubscriptionMsg from '../../dialog/sendSubscriptionMsg.vue';
import { useI18n } from 'vue-i18n';
const { t } = useI18n();
import { useNewPageFn } from './useNewPage.utils.js';
const {
  filterState,
  tableState,
  searchEvent,
  getPage,
  updateMiniTemplateForm,
  checkDetail,
  delateMiniTemplate,
  lookSendRecords,
  miniCustomTemplatesPreview,
  getNewPageList
} = useNewPageFn();
onBeforeMount(() => {
  getNewPageList();
});
</script>
<style lang="scss" scoped>
.preview-tips {
  margin-top: 10px;
  font-size: 13px;
  color: #ec5359;
}
</style>
