import { reactive } from 'vue';
import { message, Modal } from 'ant-design-vue';
import { i18n } from '@/local/index';
const { t } = i18n().global;
import { templatePlansList, miniappCustomTemplatesDel } from '@/api/modules/subscriptionMsgManagement.js';
export const useNewPageFn = () => {
  const filterState = reactive({
    filterParams: {},
    filterCriteriaList: [
      {
        label: t('common.creationTime'),
        key: ['searchTimeStart', 'searchTimeEnd'],
        type: 'date',
        setRange: true
      },
      {
        key: 'title',
        type: 'input',
        label:  t('common.templateTitle')
      }
    ]
  });
  const tableState = reactive({
    loading: false,
    columns: [
      {
        title: t('common.templateNumber'),
        dataIndex: 'id',
        key: 'id'
      },
      {
        title:t('common.customTemplateNumber'),
        dataIndex: 'templateNo',
        key: 'templateNo'
      },
      {
        title: t('common.templateType'),
        dataIndex: 'templates.title',
        key: 'templates.title'
      },
      {
        title:  t('common.title'),
        dataIndex: 'name',
        key: 'name'
      },
      {
        title: t('common.creationTime'),
        dataIndex: 'createdDate',
        key: 'createdDate'
      },
      {
        title: t('common.operate'),
        dataIndex: 'operation',
        key: 'operation'
      }
    ],
    newsList: [],
    pageInfo: {
      pageNumber: 1,
      pageSize: 10,
      count: 0
    },
    timplateDetailVisible: false,
    templateDetailInfo: null,
    subscriptionMsgPreview: false,
    curPlanId: '',
    sendSubscriptionMsgVisible: false
  });
  const searchEvent = query => {
    filterState.filterParams = query;
    getNewPageList(true);
  };
  const getPage = (index, size) => {
    tableState.pageInfo.pageNumber = index;
    if (size) {
      tableState.pageInfo.pageSize = size;
    }
    getNewPageList();
  };
  const updateMiniTemplateForm = async row => {
    window.WXAUTH?.jumpTo({ path: '/cus-wxmsgcenter-fe-web-admin/miniprogram/subscriptionMsgManagement/newTemplateForm', query: { id: row.id || '' } }, false);
  };
  const checkDetail = row => {
    const templateDetailInfo = {
      example: row.templates.content.split('\n'),
      templateId: row.templateId,
      title: row.templates.title,
      isTemplateHidden: true
    };
    tableState.templateDetailInfo = templateDetailInfo;
    tableState.timplateDetailVisible = true;
  };
  const miniCustomTemplatesPreview = id => {
    tableState.curPlanId = id;
    tableState.subscriptionMsgPreview = true;
  };
  const delateMiniTemplate = async id => {
    Modal.confirm({
      title: t('common.prompt'),
      content: t('common.deletePrompt'),
      class: 'warning-prompt-box-h',
      getContainer: '#wxmsgcenterapp',
      okText:t('common.confirm'),
      cancelText:t('common.cancel'),
      onCancel: function (e) {
        Modal.destroyAll();
      },
      onOk: async function (e) {
        Modal.destroyAll();
        await miniappCustomTemplatesDel(id);
        message.success(t('common.operationSuccessful'));
        getNewPageList(true);
      }
    });
  };
  const lookSendRecords = id => {
    window.WXAUTH?.jumpTo({ path: '/cus-wxmsgcenter-fe-web-admin/miniprogram/subscriptionMsgManagement/sendRecords', query: { id } }, false);
  };
  const getNewPageList = async reset => {
    if (reset) {
      tableState.pageInfo.pageNumber = 1;
    }
    const startTime = ' 00:00:00',
      endTime = ' 23:59:59';
    const { title, searchTimeStart, searchTimeEnd } = filterState.filterParams;
    const par = {
      action: 'list',
      moniappTemplateId: tableState.moniappTemplateId,
      ...tableState.pageInfo,
      count: undefined,
      title,
      searchTimeStart: searchTimeStart ? `${searchTimeStart}${startTime}` : '',
      searchTimeEnd: searchTimeEnd ? `${searchTimeEnd}${endTime}` : ''
    };
    const {
      data: { list, totalCount }
    } = await templatePlansList(par);
    tableState.newsList = list || [];
    tableState.pageInfo.count = Number(totalCount);
  };
  return {
    filterState,
    tableState,
    miniCustomTemplatesPreview,
    searchEvent,
    getPage,
    updateMiniTemplateForm,
    checkDetail,
    delateMiniTemplate,
    lookSendRecords,
    getNewPageList
  };
};
