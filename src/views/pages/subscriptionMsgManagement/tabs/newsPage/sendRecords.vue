<template>
  <container>
    <template #header>
      <navHeader :title="t('common.sendRecords')" :goBack="goBack" />
    </template>
    <template #content>
      <div class="general-padding20 genelal-theme-card-bg">
        <a-table :pagination="false" :dataSource="tableState.sendRecordList" :columns="MINI_SEND_RECORDS_TABLE">
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'status'">
              <a-progress
                v-if="record.status == 2"
                class="progress-area"
                size="small"
                :percent="tableState.listenMiniSendStatusIds[record.taskId] ? tableState.listenMiniSendStatusIds[record.taskId] : 0"
              ></a-progress>
              <span v-else>
                {{ SUBSCRIPTION_MSG_STATUS[record.status] ? SUBSCRIPTION_MSG_STATUS[record.status][0] : '--' }}
              </span>
            </template>
            <template v-else-if="column.key === 'operation'">
              <span v-if="record.status != 0 && record.status != 5" class="general-link-to" @click="miniTaskInterruption(record, 'cancel')"> {{t('common.cancelSend')}} </span>
              <span v-if="record.status == 2" class="general-link-to" @click="miniTaskInterruption(record, 'stop')"> {{t('common.pauseSend')}} </span>
              <span v-if="record.status == 4" class="general-link-to" @click="miniTaskInterruption(record, 'continue')"> {{t('common.continueSend')}} </span>
              <span v-if="record.status == 0 || record.status == 5" class="general-link-to" @click="sendreport(record)"> {{t('common.sendReport')}} </span>
            </template>
          </template>
        </a-table>
        <pagination :showSizes="true" :page="tableState.pageInfo.pageNumber" :pageSize="tableState.pageInfo.pageSize" :count="tableState.pageInfo.count" @current-change="getMiniPage"></pagination>
      </div>
    </template>
  </container>
  <a-modal :footer="null" getContainer="#wxmsgcenterapp" :title="t('common.sendReport')" v-model:open="tableState.sendreportVisible" width="1000px" @cancel="sendreportPreviewClose">
    <div class="dialog-content">
      <a-table :pagination="false" :dataSource="tableState.sendreportList" :columns="MINI_SEND_REPORT_TABLE">
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'operation'">
            <span class="general-link-to" @click="downMinidetailed(record.taskId)">{{t('common.downloadDetails')}}</span>
          </template>
        </template>
      </a-table>
    </div>
  </a-modal>
</template>
<script setup>
import { onMounted, onUnmounted, reactive } from 'vue';
import { useRoute } from 'vue-router';
const { query } = useRoute();
import { useI18n } from 'vue-i18n';
const { t } = useI18n();
import { SUBSCRIPTION_MSG_STATUS } from '@/views/pages/commonModules/enum.js';
import { message, Modal } from 'ant-design-vue';
import { MINI_SEND_RECORDS_TABLE, MINI_SEND_REPORT_TABLE } from '@/views/pages/commonModules/tableColumnsConfig.js';
import { miniappTemplateTasksContinue, miniappTemplateTasksList, miniappTemplateTasksdownloadError } from '@/api/modules/subscriptionMsgManagement.js';
import { downloadFile } from '@/utils/function.js';
let miniTimeoutEvent = null;
const tableState = reactive({
  curId: '',
  sendRecordList: [],
  pageInfo: {
    pageNumber: 1,
    pageSize: 10,
    count: 0
  },
  sendreportVisible: false,
  sendreportList: [],
  listenMiniSendStatusIds: {}
});
const getMiniPage = (index, size) => {
  tableState.pageInfo.pageNumber = index;
  if (size) {
    tableState.pageInfo.pageSize = size;
  }
  getMiniSendRecordList();
};
const miniTaskInterruption = async (record, type) => {
  let action = '',
    text = '',
    status = '';
  switch (type) {
    case 'cancel':
      action = 'cancel';
      status = 0;
      text = t('common.cancelSend');
      break;
    case 'stop':
      action = 'stop';
      status = 4;
      text = t('common.pauseSend');
      break;
    case 'continue':
      action = 'continue';
      status = 2;
      text = t('common.continueSend');
      break;
  }
  Modal.confirm({
    title: t('common.prompt'),
    content: `${t('tplMsgManagement.sendRecordOperate')}'${text}'？`,
    class: 'warning-prompt-box-h',
    okText: t('common.confirm'),
    getContainer: '#wxmsgcenterapp',
    cancelText: t('common.cancel'),
    onCancel: function (e) {
      Modal.destroyAll();
    },
    onOk: async function (e) {
      Modal.destroyAll();
      const params = new FormData();
      params.append('taskIds', record.taskId);
      params.append('action', action);
      await miniappTemplateTasksContinue(params);
      if (type == 'continue') {
        tableState.listenMiniSendStatusIds[record.taskId] = 0;
      } else {
        delete tableState.listenMiniSendStatusIds[record.taskId];
      }
      if (miniTimeoutEvent) {
        clearInterval(miniTimeoutEvent);
      }
      const taskIdsArr = Object.keys(tableState.listenMiniSendStatusIds);
      if (taskIdsArr.length > 0) {
        miniTimeoutEvent = setInterval(() => {
          listenMiniSendStatus();
        }, 2000);
      }
      record.status = status;
      message.success(t('common.operationSuccessful'));
    }
  });
};
const sendreport = async row => {
  tableState.sendreportList = [row];
  tableState.sendreportVisible = true;
};
const sendreportPreviewClose = () => {
  tableState.sendreportVisible = false;
};

const downMinidetailed = taskId => {
  downloadFile(miniappTemplateTasksdownloadError + taskId, 'miniprogramSendReportFile');
};
const goBack = () => {
  window.WXAUTH?.replaceTo({ path: '/cus-wxmsgcenter-fe-web-admin/miniprogram/subscriptionMsgManagement', query: { activeName: 'news' } }, true);
};
const listenMiniSendStatus = async () => {
  const taskIdsArr = Object.keys(tableState.listenMiniSendStatusIds);
  if (!taskIdsArr.length) return;
  const params = new FormData();
  params.append('taskIds', taskIdsArr.join());
  params.append('action', 'listen');
  params.append('noLoading', true);
  const { data } = await miniappTemplateTasksContinue(params);
  let miniStatusIds = [];
  if (JSON.stringify(data) != '{}') {
    Object.keys(data).forEach(id => {
      if (data[id] > 99) {
        miniStatusIds.push(String(id));
        delete tableState.listenMiniSendStatusIds[id];
      } else {
        tableState.listenMiniSendStatusIds[id] = data[id];
      }
    });
  }
  if (miniStatusIds.length) {
    tableState.sendRecordList.forEach(el => {
      if (miniStatusIds.indexOf(String(el.taskId)) != -1) {
        el.status = 5;
      }
    });
  }
  const lastTaskIdsArr = Object.keys(tableState.listenMiniSendStatusIds);
  if (!lastTaskIdsArr.length) {
    clearInterval(miniTimeoutEvent);
  }
};

onUnmounted(() => {
  tableState.listenMiniSendStatusIds = {};
  clearInterval(miniTimeoutEvent);
});
const getMiniSendRecordList = async reset => {
  clearInterval(miniTimeoutEvent);
  if (!tableState.curId) return;
  tableState.listenMiniSendStatusIds = {};
  if (reset) {
    tableState.pageInfo.pageNumber = 1;
  }
  const par = {
    ...tableState.pageInfo,
    id: tableState.curId,
    count: undefined
  };
  const {
    data: { list, totalCount }
  } = await miniappTemplateTasksList(par);
  tableState.sendRecordList = list || [];
  tableState.pageInfo.count = Number(totalCount);
  tableState.sendRecordList.forEach(row => {
    if (row.status == 2) {
      tableState.listenMiniSendStatusIds[row.taskId] = 0;
    }
  });
  miniTimeoutEvent = setInterval(() => {
    listenMiniSendStatus();
  }, 2000);
};
onMounted(() => {
  const { id } = query;
  tableState.curId = id || '';
  getMiniSendRecordList();
});
</script>
<style lang="scss" scoped>
.dialog-content {
  min-height: 50vh;
}
.progress-area {
  width: 90%;
}
</style>
