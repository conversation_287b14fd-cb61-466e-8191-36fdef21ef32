<template>
  <a-modal getContainer="#wxmsgcenterapp" v-model:open="props.sendSubscriptionMsgVisible" :title="t('tplMsgManagement.sendSubscriptionMessage')" width="600px" @cancel="handleClose">
    <a-form autocomplete="off" ref="formNode" class="form-warpper" :rules="formData.formItemRules" :model="formData.formItemData">
      <a-form-item :label="t('tplMsgManagement.sendRange')" name="uploadType">
        <a-segmented v-model:value="formData.formItemData.uploadType" :options="SEND_RANGE_OPTION" />
      </a-form-item>
      <a-form-item v-if="formData.formItemData.uploadType == '3'" :label="t('tplMsgManagement.subscriptionTime')" name="subscribeBeginTime">
        <a-range-picker :getPopupContainer="getPopupContainer" show-time v-model:value="formData.formItemData.subscribeBeginTime" value-format="YYYY-MM-DD HH:mm:ss" />
      </a-form-item>
      <a-form-item v-if="formData.formItemData.uploadType == '1'" :label="t('tplMsgManagement.uploadWhitelist')" name="fileName" class="upload-area">
        <div class="flex-left-center">
          <a-upload :before-upload="() => false" :show-upload-list="false" @change="uploadChangeFile" action="" accept=".csv">
            <a-button type="primary">{{t('common.upload')}}</a-button>
          </a-upload>
          <a-button @click="download">{{ t('common.downloadTemplate') }}</a-button>
        </div>
        <p class="upload-file-name">
          {{ formData.formItemData.fileName }}
        </p>
        <p class="upload-file-tips">
          1、{{t('tplMsgManagement.uploadWhiteTips1')}}
        </p>
        <p class="upload-file-tips">2、{{t('tplMsgManagement.uploadWhiteTips2')}}</p>
      </a-form-item>
      <a-form-item :label="t('common.sendTime')" name="isPlan">
        <a-select :getPopupContainer="getPopupContainer" filterable v-model:value="formData.formItemData.isPlan" :placeholder="t('common.pleaseSelect')">
          <a-select-option :key="index" v-for="(item, index) in IS_PLAN_OPTION" :value="item.value">
            {{ item.label }}
          </a-select-option>
        </a-select>
      </a-form-item>
      <a-form-item v-if="formData.formItemData.isPlan == 1" :label="t('tplMsgManagement.plannedSendTime')" name="sendAt">
        <a-date-picker
          :getPopupContainer="getPopupContainer"
          :disabledDate="disabledDate"
          show-time
          v-model:value="formData.formItemData.sendAt"
          value-format="YYYY-MM-DD HH:mm:ss"
          :placeholder="t('common.pleaseSelect')"
        />
      </a-form-item>
      <a-form-item class="form-item-h" :label="t('tplMsgManagement.sendSpeed')" name="minuteSendCount">
        <span class="item-span-tips border-right-no">{{t('tplMsgManagement.sendEveryMinute')}}</span>
        <a-select :getPopupContainer="getPopupContainer" clearable filterable v-model:value="formData.formItemData.minuteSendCount" :placeholder="t('common.pleaseSelect')">
          <a-select-option :key="index" v-for="(item, index) in SEND_SPEED_OPTION" :value="item">
            {{ item }}
          </a-select-option>
        </a-select>
        <span class="item-span-tips border-left-no">{{t('tplMsgManagement.oneUser')}}</span>
      </a-form-item>
    </a-form>
    <template #footer>
      <div class="dialog-footer">
        <a-button @click="handleClose">{{t('common.cancel')}}</a-button>
        <a-button @click="submit" type="primary"> {{t('common.confirm')}} </a-button>
      </div>
    </template>
  </a-modal>
</template>
<script setup>
import { reactive,  ref } from 'vue';
import { message, Modal } from 'ant-design-vue';
import { useI18n } from 'vue-i18n';
const { t } = useI18n();
import { SEND_SPEED_OPTION } from '@/views/pages/commonModules/enum.js';
import { miniappCustomTemplatesSend, downloadTemplateFile } from '@/api/modules/subscriptionMsgManagement.js';
import { downloadFile } from '@/utils/function.js';
const props = defineProps({
  sendSubscriptionMsgVisible: Boolean,
  curPlanId: String | Number
});
const emits = defineEmits(['update:sendSubscriptionMsgVisible']);
let formNode = ref(null);
const IS_PLAN_OPTION = [
  { label: t('tplMsgManagement.sendNow'), value: 0 },
  { label: t('tplMsgManagement.planSend'), value: 1 }
];
const SEND_RANGE_OPTION = [
  { label: t('tplMsgManagement.pushAll'), value: 2 },
  { label:  t('tplMsgManagement.bySubscriptionTime'), value: 3 },
  { label:  t('tplMsgManagement.whiteList'), value: 1 }
];
const formData = reactive({
  sceneOption: [],
  formItemData: {
    uploadType: 2, //Sending Range
    subscribeBeginTime: [], //Subscription Time
    sceneNo: '', //By Subscription Scene
    isPlan: 0, //Sending Time
    sendAt: '', //Scheduled Sending Time
    minuteSendCount: '', //Sending Speed
    file: null,
    fileName: ''
  },
  formItemRules: {
    uploadType: [{ required: true, message: t('common.pleaseSelect'), trigger: 'change' }],
    subscribeBeginTime: [{ required: true, message: t('common.pleaseSelect'), trigger: 'change' }],
    sendAt: [{ required: true, message:t('common.pleaseSelect'), trigger: 'change' }],
    sceneNo: [{ required: true, message:t('common.pleaseSelect'), trigger: 'change' }],
    isPlan: [{ required: true, message:t('common.pleaseSelect'), trigger: 'change' }],
    minuteSendCount: [{ required: true, message:t('common.pleaseSelect'), trigger: 'change' }],
    fileName: [{ required: true, message:t('common.pleaseSelect'), trigger: 'blur' }]
  }
});
const handleClose = () => {
  emits('update:sendSubscriptionMsgVisible', false);
};
const disabledDate = currentDate => {
  const DAY_TIME = 24 * 60 * 60 * 1000;
  return currentDate && new Date(currentDate.$d).getTime() < new Date().getTime() - DAY_TIME;
};
const submit = async () => {
  if (formNode.value) {
    await formNode.value.validate();
  }
  const params = new FormData();
  const { isPlan, uploadType, minuteSendCount, sendAt, file, subscribeBeginTime } = formData.formItemData;
  params.append('planId', props.curPlanId);
  params.append('isPlan', isPlan);
  params.append('uploadType', uploadType);
  params.append('minuteSendCount', minuteSendCount);
  params.append('action', 'send');
  if (isPlan == 1) {
    params.append('sendAt', sendAt);
  }
  if (uploadType == 1) {
    params.append('file', file);
  } else if (uploadType == 3) {
    params.append('subscribeBeginTime', subscribeBeginTime[0]);
    params.append('subscribeEndTime', subscribeBeginTime[1]);
  }
  Modal.confirm({
    getContainer: '#wxmsgcenterapp',
    title:t('common.prompt'),
    content: t('common.operationTips'),
    class: 'warning-prompt-box-h',
    okText: t('common.confirm'),
    cancelText:t('common.cancel'),
    onCancel: function (e) {
      Modal.destroyAll();
    },
    onOk: async function (e) {
      Modal.destroyAll();
      await miniappCustomTemplatesSend(params);
      message.success(t('common.operationSuccessful'));
      handleClose();
    }
  });
};
const getPopupContainer = () => {
  return document.getElementById('wxmsgcenterapp');
};
const uploadChangeFile = files => {
  formData.formItemData.file = files.file;
  formData.formItemData.fileName = files.file.name;
};
const download = () => {
  downloadFile(downloadTemplateFile, 'sendTemplateFile');
};
</script>
<style lang="scss" scoped>
.form-warpper {
  padding: 0 20px;
  padding-top: 20px;
}
::v-deep.ant-form-item {
  .ant-form-item-label {
    width: 118px !important;
  }
  .ant-picker {
    width: 100%;
  }
}

.general-form-area {
  padding-top: 20px !important;
}
::v-deep.form-item-h {
  .ant-form-item-control-input-content {
    display: flex;
  }
  .item-span-tips {
    font-size: 13px;
    display: block;
    border: 1px solid #dcdfe6;
    padding: 4px 10px;
  }
  .border-right-no {
    border-right: none;
  }
  .border-left-no {
    border-left: none;
  }
  .ant-select {
    flex:1;
    .ant-select-selector{
      min-width: auto !important;
    }
  }
}

.upload-area {
  .upload-file-name {
    color: #405591;
  }
  .upload-file-tips {
    color: #96989a;
  }
}
</style>
