<template>
  <a-modal
    getContainer="#wxmsgcenterapp"
    :footer="null"
    v-model:open="props.timplateDetailVisible"
    :title="t('common.templateDetails')"
    :width="props.templateDetailInfo?.isTemplateHidden ? '500px' : '770px'"
    @cancel="handleClose"
  >
    <div class="tmp-det-area">
      <ul
        :style="{
          marginRight: props.templateDetailInfo?.isTemplateHidden ? '0' : '40px'
        }"
        class="det-area-ul"
      >
        <li class="flex-left-center">
          <a-image :preview="false" :fallback="IMAGE_ERROR_ADDRESS" class="area-ul-avatar" :src="currApplicationInfo?.logo" />
          <span>{{ currApplicationInfo?.label }}</span>
        </li>
        <li>
          <span class="li-small">{{ props.templateDetailInfo?.title }}</span>
          <p v-for="(item, index) in props.templateDetailInfo?.example" :key="index">
            {{ item }}
          </p>
        </li>
        <li>
          <span>{{t('tplMsgManagement.lookMiniProgram')}}</span>
        </li>
        <li>
          <span>{{t('tplMsgManagement.refusalNotice')}}</span>
        </li>
      </ul>
      <ul v-show="!props.templateDetailInfo?.isTemplateHidden" class="det-area-ul">
        <li>
          <span>{{t('common.templateId')}}:</span>
          <i>{{ props.templateDetailInfo?.templateId }}</i>
        </li>
        <li>
          <span>{{t('common.templateType')}}:</span>
          <i>{{ props.templateDetailInfo?.title }}</i>
        </li>
        <li>
          <span class="li-small">{{t('tplMsgManagement.keyword')}}</span>
          <p v-for="(item, index) in props.templateDetailInfo?.content" :key="index">
            {{ item }}
          </p>
          <p class="tips-p">{{ t('tplMsgManagement.templateDetailsTips') }}</p>
        </li>
      </ul>
    </div>
  </a-modal>
</template>
<script setup>
import { computed } from 'vue';
import { useI18n } from 'vue-i18n';
const { t } = useI18n();
import { getValFun } from '@/views/layout/sysApplicationInfoUtils.js';
const currApplicationInfo = computed(() => getValFun.currApplicationInfo());
import { IMAGE_ERROR_ADDRESS } from '@/views/pages/commonModules/enum.js';
const props = defineProps({
  timplateDetailVisible: Boolean,
  templateDetailInfo: Object
});
const emits = defineEmits(['update:timplateDetailVisible']);
const handleClose = () => {
  emits('update:timplateDetailVisible', false);
};
</script>
<style lang="scss" scoped>
::v-deep .a-modal__body {
  background: #f4f9ff;
}
::v-deep.tmp-det-area {
  display: flex;
  justify-content: space-between;
  padding: 15px 30px;
  padding-bottom: 20px;
  .det-area-ul {
    flex: 1;
    background: white;
    border-radius: 4px;
    padding: 0 20px;
    box-shadow: 0 0 10px #e0e0e0;
    overflow: hidden;
    li {
      padding: 8px 0;
      border-bottom: 1px solid #ededed;
      .area-ul-avatar {
        width: 40px;
        height: 40px;
        margin-right: 10px;
      }
      span {
        font-size: 14px;
        color: black;
      }
      .li-small {
        margin-bottom: 19px;
        font-size: 16px;
        display: block;
        font-weight: bold;
      }
      p {
        margin-top: 10px;
        line-height: 20px;
        color: #8f8b8b;
      }
      i {
        word-break: break-all;
        margin-left: 10px;
      }
      .tips-p {
        font-size: 13px;
        color: #929394;
        margin-top: 21px;
      }
      &:last-child {
        border-bottom: none;
      }
    }
    &:first-child {
      margin-right: 40px;
    }
  }
}
</style>
