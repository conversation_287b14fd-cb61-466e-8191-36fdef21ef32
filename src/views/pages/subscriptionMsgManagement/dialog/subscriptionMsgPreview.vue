<template>
  <a-modal getContainer="#wxmsgcenterapp" v-model:open="props.subscriptionMsgPreview" :title="t('tplMsgManagement.subscriptionMessagePreview')" width="500px" @cancel="subscriptionMsgPreviewClose">
    <div>
      <a-textarea :placeholder="t('tplMsgManagement.templateMessagePreviewTips')" :auto-size="{ minRows: 3 }" v-model:value="openIds"></a-textarea>
      <p class="preview-tips">*{{ t('tplMsgManagement.openidPreviewTips') }}</p>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <a-button @click="subscriptionMsgPreviewClose">{{ t('common.cancel') }}</a-button>
        <a-button @click="miniSubscriptionMsgPreviewSubmit" type="primary"> {{ t('common.confirm') }} </a-button>
      </div>
    </template>
  </a-modal>
</template>
<script setup>
import { ref } from 'vue';
import { message, Modal } from 'ant-design-vue';
import { useI18n } from 'vue-i18n';
const { t } = useI18n();
import { miniappCustomTemplatesPreview } from '@/api/modules/subscriptionMsgManagement.js';
const props = defineProps({
  subscriptionMsgPreview: Boolean,
  curPlanId: String | Number
});
const emits = defineEmits(['update:subscriptionMsgPreview']);
let openIds = ref('');
const subscriptionMsgPreviewClose = () => {
  openIds.value = '';
  emits('update:subscriptionMsgPreview', false);
};
const miniSubscriptionMsgPreviewSubmit = async () => {
  if (!openIds.value) {
    message.error(t('common.pleaseEnter') + ' Openid');
    return;
  }
  Modal.confirm({
    title: t('common.prompt'),
    content: t('common.operationTips'),
    class: 'warning-prompt-box-h',
    getContainer: '#wxmsgcenterapp',
    okText: t('common.confirm'),
    cancelText: t('common.cancel'),
    onCancel: function (e) {
      Modal.destroyAll();
    },
    onOk: async function (e) {
      Modal.destroyAll();
      await miniappCustomTemplatesPreview({ id: props.curPlanId, openid: openIds.value, action: 'preview' });
      message.success(t('common.operationSuccessful'));
      subscriptionMsgPreviewClose();
    }
  });
};
</script>

<style lang="scss" scoped>
.preview-tips {
  margin-top: 10px;
  font-size: 13px;
  color: #ec5359;
}
</style>