<template>
  <container >
    <template #header>
     <navHeader :title="t('tplMsgManagement.miniProgramTemplateManagement')" />
      <a-tabs v-model:activeKey="activeName">
        <a-tab-pane :tab="t('tplMsgManagement.myTemplate')" key="template"></a-tab-pane>
        <a-tab-pane :tab="t('tplMsgManagement.subscriptionMessage')" key="news"></a-tab-pane>
      </a-tabs>
    </template>
    <template #content>
        <newsPage v-if="activeName === 'news'" />
        <templatePage v-if="activeName === 'template'" />
    </template>
  </container>
</template>
<script setup>
import { ref, onBeforeMount } from "vue";
import newsPage from "./tabs/newsPage/index.vue";
import templatePage from "./tabs/templatePage/index.vue";
import { useRoute } from "vue-router";
import { useI18n } from 'vue-i18n';
const { t } = useI18n();
const route = useRoute();
let activeName = ref("template");
onBeforeMount(() => {
  activeName.value = route.query.activeName || "template";
});
</script>

