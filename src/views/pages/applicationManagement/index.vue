<template>
  <container>
    <template #content>
      <div class="general-padding20 genelal-theme-card-bg general-mt20">
        <div class="general-mb20">
          <a-button @click="updateFormEvent('service')" type="primary"> + {{ t('applicationManagement.addWeChatOfficialAccount') }}</a-button>
          <a-button class="general-ml0" @click="updateFormEvent('miniapp')" type="primary">  + {{ t('applicationManagement.addMiniProgram') }}</a-button>
          <a-button class="general-ml0" @click="lookUserAssignment('service')" type="primary">{{ t('applicationManagement.wechatOfficialAccountUserAssignment') }} </a-button>
          <a-button class="general-ml0" @click="lookUserAssignment('miniapp')" type="primary">{{ t('applicationManagement.miniProgramUserAllocation') }}</a-button>
        </div>
        <a-table :pagination="false" :dataSource="applicationListData" :columns="tableColumns">
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'operation'">
              <span class="general-link-to" @click="updateFormEvent(record.type, record.id)"> {{ t('common.view') }} </span>
            </template>
          </template>
        </a-table>
      </div>
    </template>
  </container>
</template>
<script setup>
import { onBeforeMount, computed } from 'vue';
import { useI18n } from 'vue-i18n';
const { t } = useI18n();
import { useStore } from 'vuex';
const { state, dispatch } = useStore();
const applicationListData = computed(() => state.systemApplicationInfo.applicationList);
const tableColumns = [
  {
    title: t('common.number'), 
    dataIndex: 'id',
    key: 'id'
  },
  {
    title: t('common.applicationName'),
    dataIndex: 'wechatName',
    key: 'wechatName'
  },
  {
    title:t('common.applicationType'),
    dataIndex: 'appTypeName',
    key: 'appTypeName'
  },
  {
    title:t('common.creationTime') ,
    dataIndex: 'createdDate',
    key: 'createdDate'
  },
  {
    title:  t('common.operate'),
    dataIndex: 'operation',
    key: 'operation'
  }
];
const updateFormEvent = (type, id) => {
  const path = type === 'miniapp' ? '/cus-wxmsgcenter-fe-web-admin/applicationManagement/updateMiniProgramForm' : '/cus-wxmsgcenter-fe-web-admin/applicationManagement/updateWatchForm';
  window.WXAUTH?.jumpTo({ path, query: { id: id || '' } }, false);
};
const lookUserAssignment = type => {
  const path = type === 'miniapp' ? '/cus-wxmsgcenter-fe-web-admin/applicationManagement/userAssignmentMiniprogram' : '/cus-wxmsgcenter-fe-web-admin/applicationManagement/userAssignmentOffiaccount';
  window.WXAUTH?.jumpTo({ path }, false);
};
onBeforeMount(() => {
  dispatch('getApplicationList');
});
</script>
