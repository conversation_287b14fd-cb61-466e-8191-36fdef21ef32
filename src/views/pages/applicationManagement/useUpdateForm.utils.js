import { ref, reactive } from 'vue';
import { message, Modal } from 'ant-design-vue';
import { copyProperty } from '@/utils/function';
import { i18n } from '@/local/index';
const { t } = i18n().global;
import { miniWechatSave, miniWechatDetail, wechaApplicationsSave, wechaApplicationsDetail } from '@/api/modules/applicationManagement.js';
const VUE_APP_ENVIRONMENT = process.env.VUE_APP_ENVIRONMENT; //environment variable
// The primary domain name set for server addresses
const SERVER_PRIMARY_DOMAIN_URL = Object.freeze({
  dev: {
    0: 'https://dev-galassia-api.kering.cn',
    1: 'https://qa-galassia-openapi.kering.cn/services/dev-cus-api'
  },
  qa: {
    0: 'https://qa-galassia-api.kering.cn',
    1: 'https://qa-galassia-openapi.kering.cn/services/qa-cus-api'
  },
  preprod: {
    0: 'https://preprod-galassia-api.kering.cn',
    1: 'https://qa-galassia-openapi.kering.cn/services/preprod-cus-api'
  },
  prod: {
    0: 'https://galassia-api.kering.cn',
    1: 'https://galassia-openapi.kering.cn/services/cus-api'
  }
});
// Get static application avatars
const readApplicationLogoFileFn = () => {
  let imgPathArr = [];
  const curHostUrl = 'https://' + window.location.host;
  if (process.env.NODE_ENV != 'test') {
    const images = require.context('@/../public/applicationLogoIcon', false, /\.(png|jpe?g|gif)$/);
    images.keys().map(imageName => {
      const imageNameArr = imageName.split('./');
      let pathObj = {
        imgName: imageNameArr[1],
        imgPath: curHostUrl + '/dist/cus-wxmsgcenter-fe-web-admin/applicationLogoIcon/' + imageNameArr[1]
      };
      imgPathArr.push(pathObj);
    });
  }
  return imgPathArr;
};
//operationType: 1：offiaccount-公众号 、 2：miniprogram-小程序
export const useUpdateFormFn = operationType => {
  let formNode = ref(null);
  const formData = reactive({
    curAppInfoId: '',
    serverAddress: '',
    formItemData: {
      logo: '',
      wechatName: '',
      appid: '',
      wechatOriginalId: '',
      url: '',
      isWechatAc: true
    }
  });
  const formItemRules = {
    appid: [{ required: true, message: `${t('common.pleaseEnter')} AppId`, trigger: 'blur' }],
    url: [{ required: true, message: `${t('common.pleaseEnter')} url`, trigger: 'blur' }]
  };
  const logoList = readApplicationLogoFileFn();
  const submit = async () => {
    if (formNode.value) {
      await formNode.value.validate();
    }
    Modal.confirm({
      getContainer: '#wxmsgcenterapp',
      title: t('common.prompt'),
      content: t('common.operationTips'),
      class: 'warning-prompt-box-h',
      okText: t('common.confirm'),
      cancelText:t('common.cancel'),
      onCancel: function (e) {
        Modal.destroyAll();
      },
      onOk: async function (e) {
        Modal.destroyAll();
        const params = {
          id: formData.curAppInfoId ? formData.curAppInfoId : undefined,
          type: operationType === 'miniprogram' ? 'miniapp' : 'service',
          ...formData.formItemData,
          url: formData.formItemData.isWechatAc ? '' : formData.formItemData.url
        };
        if (operationType === 'offiaccount') {
          await wechaApplicationsSave(params);
        } else {
          await miniWechatSave(params);
        }
        message.success(t('common.operationSuccess'));
      }
    });
  };
  const goBack = () => {
    window.WXAUTH?.replaceTo({ path: '/cus-wxmsgcenter-fe-web-admin/applicationManagement' }, true);
  };
  const getAppInfo = async id => {
    if (!id) return;
    let data = {};
    if (operationType === 'offiaccount') {
      data = await wechaApplicationsDetail(id);
    } else {
      data = await miniWechatDetail(id);
    }
    if (data.data) {
      formData.formItemData = copyProperty(formData.formItemData, data.data);
      const { accesstokenFetch, isWechatAc, connectUrl } = data.data;
      formData.formItemData.url = accesstokenFetch ? JSON.parse(accesstokenFetch).url : '';
      const isWechatAcNum = isWechatAc ? 1 : 0;
      if (SERVER_PRIMARY_DOMAIN_URL.hasOwnProperty(VUE_APP_ENVIRONMENT)) {
        formData.serverAddress = SERVER_PRIMARY_DOMAIN_URL[VUE_APP_ENVIRONMENT][isWechatAcNum] + connectUrl;
      }
    }
  };
  const copyLink = async () => {
    await navigator.clipboard.writeText(formData.serverAddress);
    message.success(t('common.copySuccess'));
  };
  return {
    logoList,
    formItemRules,
    formNode,
    formData,
    submit,
    copyLink,
    goBack,
    getAppInfo
  };
};
