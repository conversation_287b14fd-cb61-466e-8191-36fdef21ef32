<template>
  <container mainBgColor="#f0f2f5">
    <template #header>
      <navHeader :title="t('applicationManagement.updateMiniProgram')" :goBack="goBack" :functionBtnFn="submit" />
    </template>
    <template #content>
      <a-form autocomplete="off" class="general-form-area application-form-area" ref="formNode" :rules="formItemRules" :model="formData.formItemData">
        <a-form-item :label="t('applicationManagement.miniProgramAvatar')">
          <a-select :getPopupContainer="getPopupContainer" :placeholder="t('applicationManagement.miniProgramAvatarPrompt')" allowClear v-model:value="formData.formItemData.logo">
            <a-select-option :key="index" v-for="(imgUrl, index) in logoList" :value="imgUrl.imgPath">
              {{ imgUrl.imgName }}
            </a-select-option>
          </a-select>
          <a-image :fallback="IMAGE_ERROR_ADDRESS" :preview="false" :src="formData.formItemData.logo" />
        </a-form-item>
        <a-form-item :label="t('applicationManagement.miniProgramDescription')">
          <a-input show-count :maxlength="20" v-model:value="formData.formItemData.wechatName" :placeholder="t('applicationManagement.miniProgramDescriptionPrompt')"></a-input>
        </a-form-item>
        <a-form-item label="AppId" name="appid">
          <a-input :disabled="formData.curAppInfoId ? true : false" v-model:value="formData.formItemData.appid" :placeholder="t('common.pleaseEnter')"></a-input>
          <p class="form-item-tips">{{ t('applicationManagement.sourceOfTheMiniProgramAppID') }}</p>
        </a-form-item>
        <a-form-item :label="t('applicationManagement.weChatAcSource')">
          <a-radio-group v-model:value="formData.formItemData.isWechatAc">
            <a-radio-button :value="true">{{ t('common.yes') }}</a-radio-button>
            <a-radio-button :value="false">{{ t('common.no') }}</a-radio-button>
          </a-radio-group>
        </a-form-item>
        <a-form-item v-if="!formData.formItemData.isWechatAc" label="url" name="url">
          <a-input v-model:value="formData.formItemData.url"></a-input>
        </a-form-item>
        <a-form-item :label="t('applicationManagement.originalId')">
          <a-input v-model:value="formData.formItemData.wechatOriginalId" :placeholder="t('common.pleaseEnter')"></a-input>
          <p class="form-item-tips">{{ t('applicationManagement.originalIdPrompt') }}</p>
        </a-form-item>
        <a-form-item :label="t('applicationManagement.serverAddress')">
          <a-input-group compact>
            <a-input id="servier-address-input" disabled  v-model:value="formData.serverAddress" style="width: calc(100% - 70px)" />
            <a-button :disabled="!formData.serverAddress" @click="copyLink">{{ t('common.copy') }}</a-button>
          </a-input-group>
        </a-form-item>
      </a-form>
    </template>
  </container>
</template>
<script setup>
import { onMounted } from 'vue';
import { useI18n } from 'vue-i18n';
const { t } = useI18n();
import { useRoute } from 'vue-router';
const { query } = useRoute();
import { IMAGE_ERROR_ADDRESS } from '@/views/pages/commonModules/enum.js';
import { useUpdateFormFn } from './useUpdateForm.utils.js';
const { formNode, formData, formItemRules, submit, copyLink, goBack, getAppInfo, logoList } = useUpdateFormFn('miniprogram');
const getPopupContainer = () => {
  return document.getElementById('wxmsgcenterapp');
};
onMounted(async () => {
  const { id } = query;
  formData.curAppInfoId = id;
  getAppInfo(id);
});
</script>
<style lang="scss" scoped>
.general-form-area .ant-select,
.general-form-area .ant-input,
.general-form-area .ant-input-affix-wrapper,
.general-form-area .ant-input-group {
  width: 651px !important;
}
::v-deep.general-form-area .ant-input-affix-wrapper .ant-input {
  width: 600px !important;
}
.general-form-area .ant-input-group {
  display: flex !important;
}
::v-deep .application-form-area {
  .ant-form-item-label {
    width: 243px !important;
  }
}
::v-deep.general-form-area .ant-image {
  width: 100px !important;
  height: 100px !important;
  display: block;
  margin-top: 10px;
  border: 1px solid #e7e7e7;
  border-radius: 3px;
}
.form-item-tips {
  display: block;
  width: 100%;
  font-size: 12px;
  margin-top: 5px;
  color: #a8abb9;
}
</style>
