export default [
  {
    path: '/cus-wxmsgcenter-fe-web-admin/offiaccount/tplMsgManagement',
    name: 'cus-wxmsgcenter-fe-web-admin-offiaccount-tplMsgManagement',
    component: () => import('@/views/pages/tplMsgManagement/index.vue')
  },
  {
    path: '/cus-wxmsgcenter-fe-web-admin/offiaccount/controlManagement',
    name: 'cus-wxmsgcenter-fe-web-admin-offiaccount-tplMsgManagement-controlManagement',
    component: () => import('@/views/pages/tplMsgManagement/controlManagement.vue')
  },
  {
    path: '/cus-wxmsgcenter-fe-web-admin/applicationManagement/userAssignmentOffiaccount',
    name: 'cus-wxmsgcenter-fe-web-admin-offiaccount-tplMsgManagement-userAssignment',
    component: () => import('@/views/pages/tplMsgManagement/userAssignment.vue')
  },
  {
    path: '/cus-wxmsgcenter-fe-web-admin/offiaccount/tplMsgManagement/newTemplateForm',
    name: 'cus-wxmsgcenter-fe-web-admin-offiaccount-tplMsgManagement-newTemplateForm',
    component: () => import('@/views/pages/tplMsgManagement/tabs/customTemplate/newTemplateForm.vue')
  },
  {
    path: '/cus-wxmsgcenter-fe-web-admin/offiaccount/tplMsgManagement/historicalData',
    name: 'cus-wxmsgcenter-fe-web-admin-offiaccount-tplMsgManagement-historicalData',
    component: () => import('@/views/pages/tplMsgManagement/tabs/customTemplate/historicalData.vue')
  },
  {
    path: '/cus-wxmsgcenter-fe-web-admin/offiaccount/pushOfMa',
    name: 'cus-wxmsgcenter-fe-web-admin-offiaccount-pushOfMa',
    component: () => import('@/views/pages/tplMsgManagement/pushOfMa.vue')
  },
  {
    path: '/cus-wxmsgcenter-fe-web-admin/offiaccount/sfmc',
    name: 'cus-wxmsgcenter-fe-web-admin-sfmc-offiaccount-sfmc',
    component: () => import('@/views/pages/sendLogs/index.vue')
  },
];
