export default [
  {
    path: '/cus-wxmsgcenter-fe-web-admin/miniprogram/subscriptionMsgManagement',
    name: 'cus-wxmsgcenter-fe-web-admin-miniprogram/subscriptionMsgManagement',
    component: () => import('@/views/pages/subscriptionMsgManagement/index.vue')
  },
  {
    path: '/cus-wxmsgcenter-fe-web-admin/miniprogram/controlManagement',
    name: 'cus-wxmsgcenter-fe-web-admin-miniprogram-subscriptionMsgManagement-controlManagement',
    component: () => import('@/views/pages/subscriptionMsgManagement/controlManagement.vue')
  },
  {
    path: '/cus-wxmsgcenter-fe-web-admin/applicationManagement/userAssignmentMiniprogram',
    name: 'cus-wxmsgcenter-fe-web-admin-miniprogram-subscriptionMsgManagement-userAssignment',
    component: () => import('@/views/pages/subscriptionMsgManagement/userAssignment.vue')
  },
  {
    path: '/cus-wxmsgcenter-fe-web-admin/miniprogram/subscriptionMsgManagement/subscriptionData',
    name: 'cus-wxmsgcenter-fe-web-admin-miniprogram/subscriptionMsgManagement/subscriptionData',
    component: () => import('@/views/pages/subscriptionMsgManagement/tabs/templatePage/subscriptionData.vue')
  },
  {
    path: '/cus-wxmsgcenter-fe-web-admin/miniprogram/subscriptionMsgManagement/newTemplateForm',
    name: 'cus-wxmsgcenter-fe-web-admin-miniprogram/subscriptionMsgManagement/newTemplateForm',
    component: () => import('@/views/pages/subscriptionMsgManagement/tabs/newsPage/newTemplateForm.vue')
  },
  {
    path: '/cus-wxmsgcenter-fe-web-admin/miniprogram/subscriptionMsgManagement/sendRecords',
    name: 'cus-wxmsgcenter-fe-web-admin-miniprogram/subscriptionMsgManagement/sendRecords',
    component: () => import('@/views/pages/subscriptionMsgManagement/tabs/newsPage/sendRecords.vue')
  },
  {
    path: '/cus-wxmsgcenter-fe-web-admin/miniprogram/pushOfMa',
    name: 'cus-wxmsgcenter-fe-web-admin-miniprogram-pushOfMa',
    component: () => import('@/views/pages/subscriptionMsgManagement/pushOfMa.vue')
  },

];
