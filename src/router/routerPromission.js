import router from '@/router';
import store from '@/store';
import { message } from 'ant-design-vue';
import { i18n } from '@/local/index';
const { t } = i18n().global;
// 1.Verify user identity
const verifyIdentity = () => {
  const accessToken = sessionStorage.getItem('accessToken');
  if (!accessToken) {
    message.error(t('enum.assessTokenFailed'));
  }
  return accessToken;
};
// 2.Determine whether the application app exists
const getApplication = async path => {
  await store.dispatch('getApplicationList', path);
};
// 3.Determine whether the current route requires the application of appid
const handleIndividualRoute = async to => {
  const path = to.path;
  if (path.includes('/offiaccount') || path.includes('/miniprogram')) {
    await getApplication(path);
    store.dispatch('defaultCurApplicationData', path);
  }
};
router.beforeEach(async (to, from, next) => {
  const isVerified = verifyIdentity();
  if (isVerified) {
    await handleIndividualRoute(to);
    next();
  }
});
