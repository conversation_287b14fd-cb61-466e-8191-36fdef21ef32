import layout from '@/views/layout';
import applicationManagement from './modules/applicationManagement.js';
import controlManagement from './modules/controlManagement.js'; //Central control routing
import subscriptionMsgManagement from './modules/subscriptionMsgManagement.js';
import tplMsgManagement from './modules/tplMsgManagement.js';
export default [
  {
    path: '/',
    component: layout,
    name: 'layout',
    children: [...applicationManagement, ...controlManagement, ...subscriptionMsgManagement, ...tplMsgManagement]
  }
];
