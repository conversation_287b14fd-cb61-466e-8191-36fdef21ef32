const Timestamp = new Date().getTime();
const TerserPlugin = require('terser-webpack-plugin');
const packageJson = require('./package.json');
// eslint-disable-next-line no-undef
module.exports = {
  // eslint-disable-next-line no-undef
  publicPath: `/dist/${packageJson.name}`,
  productionSourceMap: false,
  devServer: {
    hot: true,
    port: 8083,
    overlay: {
      warnings: false,
      errors: false
    }
  },
  configureWebpack: {
    optimization: {
      minimizer: [
        new TerserPlugin({
          terserOptions: { compress: { drop_console: true } }
        })
      ],
      splitChunks: {
        cacheGroups: {
          common: {
            name: 'chunk-common',
            chunks: 'initial',
            minChunks: 1,
            maxInitialRequests: 5,
            minSize: 0,
            priority: 1,
            reuseExistingChunk: true
          },
          vendors: {
            name: 'chunk-vendors',
            test: /[\\/]node_modules[\\/]/,
            chunks: 'initial',
            maxSize: 600000,
            maxInitialRequests: 20,
            priority: 2,
            reuseExistingChunk: true,
            enforce: true
          }
        }
      }
    },
    output: {
      filename: `js/[name].${packageJson.name}.${Timestamp}.js`,
      chunkFilename: `js/[name].${packageJson.name}.${Timestamp}.js`,
      library: packageJson.name,
      libraryTarget: 'umd',
      jsonpFunction: `webpackJsonp_${packageJson.name}`
    }
  },
  lintOnSave: false
};
