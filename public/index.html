<!DOCTYPE html>
<html lang="">

<head>
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width,initial-scale=1.0">
  <meta http-equiv="Cache-Control" content="no-siteapp, no-cache, no-store, must-revalidate" />
  <meta http-equiv="Pragma" content="no-cache" />
  <meta http-equiv="Expires" content="0" />
  <title>
    <%= htmlWebpackPlugin.options.title %>
  </title>
</head>

<body>
  <noscript>
    <strong>We're sorry but <%= htmlWebpackPlugin.options.title %> doesn't work properly without JavaScript enabled.
        Please enable it to continue.</strong>
  </noscript>
  <div id='wxmsgcenterapp'></div>
  <!-- built files will be auto injected -->
  <% const date=new Date() %>
    <% date.setHours(date.getHours() + 8) %>
      <% const bundleTime=date.toISOString().slice(0, 19).replace('T',' ') %>
    <script>console.log(' ENV: DEV\nBUILD TIME: <%=bundleTime %>')</script>
</body>

</html>