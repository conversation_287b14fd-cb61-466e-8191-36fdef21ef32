# This workflow will build a Java project with <PERSON><PERSON>, and cache/restore any dependencies to improve the workflow execution time
# For more information see: https://docs.github.com/en/actions/automating-builds-and-tests/building-and-testing-java-with-maven

# This workflow uses actions that are not certified by GitHub.
# They are provided by a third-party and are governed by
# separate terms of service, privacy policy, and support
# documentation.

name: (Manual) Create Release

on:
  workflow_dispatch
  
run-name: Create release from ${{ github.sha }}

env:
  ARTIFACTORY_DOCKER_REGISTRY: ${{ vars.ARTIFACTORY_DOCKER_REGISTRY }}
  VAULT_ADDRESS_PROD: ${{ vars.VAULT_ADDRESS_PROD }}
  VAULT_GITHUB_ROLE: ${{ vars.VAULT_GITHUB_ROLE }}
  VAULT_GITHUB_PATH: ${{ vars.VAULT_GITHUB_PATH }}
  vars: ${{ toJSON(vars) }}

jobs:
  create-branch:
    permissions:
      contents: read
      id-token: write
    runs-on: kt-gu-cn-heavy
    steps:
      - uses: kering-technologies-china/dso-cus-github-action/microservice/setup@main
        name: Setup Environment
      - uses: actions/checkout@v4
        with:
          token: ${{ env.GITHUB_API_TOKEN }} # Github Personal Access Token
      - uses: kering-technologies-china/dso-cus-github-action/create-release-branch@main
        name: Create release branch
        id: create_release_branch
      - name: Display release branch name
        run: |
          echo "the new release branch name is ${{ steps.create_release_branch.outputs.release_branch_name}}."
          echo "the create release branch result is ${{ steps.create_release_branch.outputs.branch_result}}."