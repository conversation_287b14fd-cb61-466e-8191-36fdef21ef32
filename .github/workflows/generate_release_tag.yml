# This workflow will build a Java project with <PERSON><PERSON>, and cache/restore any dependencies to improve the workflow execution time
# For more information see: https://docs.github.com/en/actions/automating-builds-and-tests/building-and-testing-java-with-maven

# This workflow uses actions to create tag for release

name: (Auto) Generate Release Tag

on:
  push:
    branches:
      - 'release-*'
      
env:
  ARTIFACTORY_DOCKER_REGISTRY: ${{ vars.ARTIFACTORY_DOCKER_REGISTRY }}
  VAULT_ADDRESS_PROD: ${{ vars.VAULT_ADDRESS_PROD }}
  VAULT_GITHUB_ROLE: ${{ vars.VAULT_GITHUB_ROLE }}
  VAULT_GITHUB_PATH: ${{ vars.VAULT_GITHUB_PATH }}
  vars: ${{ toJSON(vars) }}

jobs:
  create_tag:
    permissions:
      contents: read
      id-token: write
    runs-on:
      - kt-gu-cn-heavy
    steps:
      - uses: kering-technologies-china/dso-cus-github-action/microservice/setup@main
        name: Setup Environment
      - uses: actions/checkout@v4
        with:
          token: ${{ env.GITHUB_API_TOKEN }} # Github Personal Access Token
      - uses: kering-technologies-china/dso-cus-github-action/create-release-tag@main
        name: Create release tag
        id: create_relese_tag
        with:
          branch_name: ${{ github.ref_name }}
      - name: Display release tag name
        run: echo "the new release tag name is ${{ steps.create_relese_tag.outputs.tag_name}}."