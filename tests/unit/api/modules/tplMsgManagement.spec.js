import './mockAxiosConfigHttp.js';
import {
  customTemplates,
  templatesList,
  checkTemplate,
  createTemplatePlans,
  templatePlansEdit,
  detailTemplatePlans,
  templatePlansDel,
  getPlanHistory,
  templateTasksList,
  customTemplateMsgAction,
  templatesEdit,
  downloadError,
  offMaSendResultList,
  messageChannelList,
  featchAuthorityList,
  syncAuthorityList,
  delAuthorityList,
  updateAuthorityList,
} from '@/api/modules/tplMsgManagement.js';

describe('test the methods in the tplMsgManagement file', () => {
  it('test featchAuthorityList api request', async () => {
    const { data } = await featchAuthorityList();
    expect(data).toBe(1);
  });
  it('test syncAuthorityList api request', async () => {
    const { data } = await syncAuthorityList();
    expect(data).toBe(1);
  });
  it('test delAuthorityList api request', async () => {
    const { data } = await delAuthorityList();
    expect(data).toBe(1);
  });
  it('test updateAuthorityList api request', async () => {
    const { data } = await updateAuthorityList();
    expect(data).toBe(1);
  });
  it('test messageChannelList api request', async () => {
    const { data } = await messageChannelList();
    expect(data).toBe(1);
  });
  it('test customTemplates api request', async () => {
    const { data } = await customTemplates();
    expect(data).toBe(1);
  });
  it('test templatesList api request', async () => {
    const { data } = await templatesList();
    expect(data).toBe(1);
  });
  it('test checkTemplate api request', async () => {
    const { data } = await checkTemplate(1);
    expect(data).toBe(1);
  });
  it('test createTemplatePlans api request', async () => {
    const { data } = await createTemplatePlans();
    expect(data).toBe(1);
  });
  it('test templatePlansEdit api request', async () => {
    const { data } = await templatePlansEdit();
    expect(data).toBe(1);
  });
  it('test detailTemplatePlans api request', async () => {
    const { data } = await detailTemplatePlans();
    expect(data).toBe(1);
  });
  it('test templatePlansDel api request', async () => {
    const { data } = await templatePlansDel();
    expect(data).toBe(1);
  });
  it('test getPlanHistory api request', async () => {
    const { data } = await getPlanHistory({ planId: 1 });
    expect(data).toBe(1);
  });
  it('test templateTasksList api request', async () => {
    const { data } = await templateTasksList();
    expect(data).toBe(1);
  });
  it('test customTemplateMsgAction api request', async () => {
    const { data } = await customTemplateMsgAction();
    expect(data).toBe(1);
  });
  it('test templatesEdit api request', async () => {
    const { data } = await templatesEdit();
    expect(data).toBe(1);
  });
  it('test downloadError api request', async () => {
    const data = await downloadError(1);
    expect(data).toContain('/custom-template-messages');
  });
  it('test offMaSendResultList api request', async () => {
    const { data } = await offMaSendResultList();
    expect(data).toBe(1);
  });
});
