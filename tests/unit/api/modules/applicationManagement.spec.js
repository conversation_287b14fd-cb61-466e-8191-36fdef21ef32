import './mockAxiosConfigHttp.js';
import { miniWechatList, miniWechatSave, miniWechatDetail, wechaApplicationsSave, wechaApplications, wechaApplicationsDetail } from '@/api/modules/applicationManagement.js';
describe('test the methods in the applicationManagement file', () => {
  it('test miniWechatSave api request', async () => {
    const { data } = await miniWechatSave();
    expect(data).toBe(1);
  });
  it('test miniWechatList api request', async () => {
    const { data } = await miniWechatList();
    expect(data).toBe(1);
  });
  it('test miniWechatDetail api request', async () => {
    const { data } = await miniWechatDetail(1);
    expect(data).toBe(1);
  });
  it('test wechaApplicationsSave api request', async () => {
    const { data } = await wechaApplicationsSave();
    expect(data).toBe(1);
  });
  it('test wechaApplications api request', async () => {
    const { data } = await wechaApplications();
    expect(data).toBe(1);
  });
  it('test wechaApplicationsDetail api request', async () => {
    const { data } = await wechaApplicationsDetail(1);
    expect(data).toBe(1);
  });
});
