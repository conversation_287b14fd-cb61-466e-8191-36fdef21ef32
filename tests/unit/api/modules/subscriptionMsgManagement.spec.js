import './mockAxiosConfigHttp.js';
import {
  miniappTemplatesList,
  miniappTemplatesCheckTemplate,
  miniappTemplatesEdit,
  templatePlansList,
  createCustomTemplate,
  miniappCustomTemplatesEdit,
  miniappCustomTemplatesDetail,
  miniappCustomTemplatesDel,
  miniappCustomTemplatesPreview,
  miniappCustomTemplatesSend,
  miniappTemplateTasksContinue,
  miniappTemplateTasksList,
  miniappTemplatesUserList,
  miniappTemplatesDownload,
  maSendResultList,
  miniMessageChannelList,
  miniFeatchAuthorityList,
  miniSyncAuthorityList,
  miniDelAuthorityList,
  miniUpdateAuthorityList
} from '@/api/modules/subscriptionMsgManagement.js';
describe('test the methods in the subscriptionMsgManagement file', () => {
  it('test miniFeatchAuthorityList api request', async () => {
    const { data } = await miniFeatchAuthorityList();
    expect(data).toBe(1);
  });
  it('test miniSyncAuthorityList api request', async () => {
    const { data } = await miniSyncAuthorityList();
    expect(data).toBe(1);
  });
  it('test miniDelAuthorityList api request', async () => {
    const { data } = await miniDelAuthorityList();
    expect(data).toBe(1);
  });
  it('test miniUpdateAuthorityList api request', async () => {
    const { data } = await miniUpdateAuthorityList();
    expect(data).toBe(1);
  });
  it('test miniappTemplatesList api request', async () => {
    const { data } = await miniappTemplatesList();
    expect(data).toBe(1);
  });
  it('test miniMessageChannelList api request', async () => {
    const { data } = await miniMessageChannelList();
    expect(data).toBe(1);
  });
  it('test miniappTemplatesCheckTemplate api request', async () => {
    const { data } = await miniappTemplatesCheckTemplate(1);
    expect(data).toBe(1);
  });
  it('test miniappTemplatesEdit api request', async () => {
    const { data } = await miniappTemplatesEdit(1);
    expect(data).toBe(1);
  });
  it('test templatePlansList api request', async () => {
    const { data } = await templatePlansList();
    expect(data).toBe(1);
  });
  it('test createCustomTemplate api request', async () => {
    const { data } = await createCustomTemplate();
    expect(data).toBe(1);
  });
  it('test miniappCustomTemplatesEdit api request', async () => {
    const { data } = await miniappCustomTemplatesEdit({ planId: 1 });
    expect(data).toBe(1);
  });
  it('test miniappCustomTemplatesDetail api request', async () => {
    const { data } = await miniappCustomTemplatesDetail(1);
    expect(data).toBe(1);
  });
  it('test miniappCustomTemplatesDel api request', async () => {
    const { data } = await miniappCustomTemplatesDel(1);
    expect(data).toBe(1);
  });

  it('test miniappCustomTemplatesPreview api request', async () => {
    const { data } = await miniappCustomTemplatesPreview();
    expect(data).toBe(1);
  });
  it('test miniappCustomTemplatesSend api request', async () => {
    const { data } = await miniappCustomTemplatesSend();
    expect(data).toBe(1);
  });
  it('test miniappTemplateTasksContinue api request', async () => {
    const { data } = await miniappTemplateTasksContinue();
    expect(data).toBe(1);
  });
  it('test miniappTemplateTasksList api request', async () => {
    const { data } = await miniappTemplateTasksList({ id: 1 });
    expect(data).toBe(1);
  });
  it('test miniappTemplatesUserList api request', async () => {
    const { data } = await miniappTemplatesUserList();
    expect(data).toBe(1);
  });

  it('test miniappTemplatesDownload api request', async () => {
    const data = await miniappTemplatesDownload({ id: 1, query: 'type' });
    expect(data).toContain('1');
  });
  it('test maSendResultList api request', async () => {
    const { data } = await maSendResultList();
    expect(data).toBe(1);
  });
});
