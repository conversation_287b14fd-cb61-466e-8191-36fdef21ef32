import './mockAxiosConfigHttp.js';
import {
  createSuppliersForm,
  updateSuppliersForm,
  getApis,
  deleteSuppliers,
  detail,
  suppliersList,
  miniCreateSuppliersForm,
  miniUpdateSuppliersForm,
  miniGetApis,
  miniDeleteSuppliers,
  miniDetail,
  miniSuppliersList
} from '@/api/modules/controlManagement.js';
describe('test the methods in the applicationManagement file', () => {
  it('test createSuppliersForm api request', async () => {
    const { data } = await createSuppliersForm();
    expect(data).toBe(1);
  });
  it('test updateSuppliersForm api request', async () => {
    const { data } = await updateSuppliersForm();
    expect(data).toBe(1);
  });
  it('test getApis api request', async () => {
    const { data } = await getApis();
    expect(data).toBe(1);
  });
  it('test deleteSuppliers api request', async () => {
    const { data } = await deleteSuppliers(1);
    expect(data).toBe(1);
  });
  it('test detail api request', async () => {
    const { data } = await detail(1);
    expect(data).toBe(1);
  });
  it('test suppliersList api request', async () => {
    const { data } = await suppliersList();
    expect(data).toBe(1);
  });
  it('test miniCreateSuppliersForm api request', async () => {
    const { data } = await miniCreateSuppliersForm();
    expect(data).toBe(1);
  });
  it('test miniUpdateSuppliersForm api request', async () => {
    const { data } = await miniUpdateSuppliersForm();
    expect(data).toBe(1);
  });
  it('test miniGetApis api request', async () => {
    const { data } = await miniGetApis();
    expect(data).toBe(1);
  });
  it('test miniDeleteSuppliers api request', async () => {
    const { data } = await miniDeleteSuppliers(1);
    expect(data).toBe(1);
  });
  it('test miniDetail api request', async () => {
    const { data } = await miniDetail(1);
    expect(data).toBe(1);
  });
  it('test miniSuppliersList api request', async () => {
    const { data } = await miniSuppliersList();
    expect(data).toBe(1);
  });
});
