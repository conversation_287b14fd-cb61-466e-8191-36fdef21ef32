import registerRequireContextHook from 'babel-plugin-require-context-hook/register';
registerRequireContextHook();
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: jest.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: jest.fn(),
    removeListener: jest.fn(),
    addEventListener: jest.fn(),
    removeEventListener: jest.fn(),
    dispatchEvent: jest.fn()
  }))
});
jest.mock('vue-i18n', () => ({
  useI18n: jest.fn(() => ({
    t: jest.fn()
  })),
  createI18n:jest.fn()
}));
jest.mock('@/local/index', () => ({
  i18n: () => ({
    global: {
      t: jest.fn(v => v)
    }
  }),
  usI18n:jest.fn()
}));
