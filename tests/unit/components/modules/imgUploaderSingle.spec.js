import { mount } from '@vue/test-utils';
import imgUploaderSingle from '@/components/modules/imgUploaderSingle.vue';
import { Upload, Modal } from 'ant-design-vue';
describe('test imgUploaderSingle components', () => {
  let wrapper;
  beforeEach(() => {
    wrapper = mount(imgUploaderSingle, {
      props: {
        width: '100',
        height: '100',
        currImgUrl: 'publicPath',
        disabled: true
      },
      global: {
        components: {
          'a-upload': Upload,
          'a-modal': Modal
        }
      }
    });
  });
  it('test currImgUrl Branch', async () => {
    await wrapper.setProps({
      currImgUrl: '',
      disabled: false
    });
    await wrapper.vm.$nextTick();
    await wrapper.setProps({
      currImgUrl: 'publicPath',
      disabled: true
    });
    await wrapper.vm.$nextTick();
    const previewNode = wrapper.find('.anticon-zoom-in');
    expect(previewNode.exists()).toBe(true);
    previewNode.trigger('click');
    const removeNode = wrapper.find('.anticon-close-circle');
    expect(removeNode.exists()).toBe(true);
    removeNode.trigger('click');
  });
  it('test upload Func', async () => {
    await wrapper.setProps({
      width: '100',
      height: '100',
      currImgUrl: '',
      disabled: true
    });
    await wrapper.vm.$nextTick();
    const uploadNode = wrapper.findComponent(Upload);
    expect(uploadNode.exists()).toBeTruthy();
    await uploadNode.vm.$emit('update:fileList', []);
  });
  it('test modal open  Func ', async () => {
    const modalNode = wrapper.findComponent(Modal);
    expect(modalNode.exists()).toBeTruthy();
    await modalNode.vm.$emit('update:open', true);
    expect(modalNode.vm.open).toBe(true);
  });
});
