import { mount } from '@vue/test-utils';
import dateDom from '@/components/modules/searchForm/items/date.vue';
import { DatePicker } from 'ant-design-vue';
describe('test searchForm components', () => {
  let wrapper;
  beforeEach(() => {
    wrapper = mount(dateDom, {
      global: {
        components: {
          'a-range-picker': DatePicker.RangePicker
        }
      },
      props: {
        item: {
          label: '时间组件',
          key: ['startTime', 'endTime'],
          type: 'date'
        },
        form: { startTime: '', endTime: '' }
      }
    });
  });
  it('if statements and methods for testing RangePicker components', async () => {
    const rangePickerNode = wrapper.findComponent(DatePicker.RangePicker);
    expect(rangePickerNode.exists()).toBeTruthy();
    const dateArr = ['2024-09-09', '2024-10-10'];
    await rangePickerNode.vm.$emit('update:value', dateArr);
    await rangePickerNode.vm.$nextTick();
    expect(rangePickerNode.vm.value).toEqual(dateArr);
  });
});
