import { mount } from '@vue/test-utils';
import selectDom from '@/components/modules/searchForm/items/select.vue';
import { Select } from 'ant-design-vue';
describe('test searchForm components', () => {
  let wrapper;
  beforeEach(() => {
    wrapper = mount(selectDom, {
      global: {
        components: {
          'a-select': Select,
          'a-select-option': Select.Option
        }
      },
      props: {
        item: {
          key: 'type',
          type: 'select',
          label: '下拉选择',
          options: [
            {
              label: '实物',
              value: 1
            },
            {
              label: '虚无',
              value: 2
            }
          ]
        },
        form: { type: '' }
      }
    });
  });
  it('if statements and methods for testing select components', async () => {
    const selectNode = wrapper.findComponent(Select);
    expect(selectNode.exists()).toBe(true);
    await selectNode.vm.$emit('update:value', 2);
    await selectNode.vm.$nextTick();
    expect(selectNode.vm.value).toBe(2);
    await selectNode.vm.$slots.default();
    await selectNode.vm.$nextTick();
    await selectNode.vm.$emit('change', 1);
    await selectNode.vm.$nextTick();
    expect(selectNode.vm.value).toBe(1);
    await wrapper.setProps({
      item: {
        key: 'type',
        type: 'select',
        label: '下拉选择',
        optValue: 'val',
        placeholder: '请选择',
        optLabel: 'name',
        notFilterable: true,
        notClearable: true
      }
    });
    expect(wrapper.html()).toContain('filterable="false"');
  });
});
