import { mount } from '@vue/test-utils';
import searchForm from '@/components/modules/searchForm/index.vue';
import { Form, Input, DatePicker, Select, Button } from 'ant-design-vue';
describe('test searchForm components', () => {
  let wrapper;
  beforeEach(() => {
    wrapper = mount(searchForm, {
      props: {
        query: { keyword: 'this is input value' },
        params: [
          {
            key: 'keyword',
            type: 'input',
            label: '输入框'
          },
          {
            label: '自定义插槽位置',
            slotName: 'customLocation'
          },
          {
            key: 'type',
            type: 'select',
            label: '下拉选择',
            options: [
              {
                label: '实物',
                value: 1
              },
              {
                label: '异业券',
                value: 2
              },
              {
                label: 'CRM',
                value: 3
              },
              {
                label: '预约服务',
                value: 5
              }
            ],
            optValue: 'value',
            optLabel: 'label',
            notFilterable: false,
            notClearable: false,
            defaultAll: true
          },
          {
            label: '时间组件',
            key: ['startTime', 'endTime'],
            type: 'date',
            valueFormat: 'YYYY-MM-DD',
            setRange: true
          },
          {
            label: '时间单个查询',
            key: 'wordTime',
            type: 'date',
            valueFormat: 'YYYY-MM-DD',
            setRange: false
          }
        ],
        showLeftBtns: true,
        showReset: false,
        showQuery: false
      },
      slots: {
        left: 'this is leftSlots',
        btns: 'this is btnsSlots',
        customLocation: 'this is customLocation'
      },
      global: {
        components: {
          'a-form': Form,
          'a-input': Input,
          'a-range-picker': DatePicker.RangePicker,
          'a-date-picker': DatePicker,
          'a-select': Select,
          'a-button': Button
        }
      }
    });
  });
  it('if statements and methods for testing and filtering component entry pages', async () => {
    await wrapper.setProps({
      params: [],
      showLeftBtns: true,
      showReset: true,
      showQuery: true
    });
    await wrapper.vm.$nextTick();
    expect(wrapper.html()).toContain('this is leftSlots');
  });
  it('if statements and methods for testing input components', async () => {
    await wrapper.setProps({
      params: [
        {
          key: 'keyword',
          type: 'input',
          placeholder: '请输入KeyWord'
        }
      ],
      showLeftBtns: false,
      showReset: true,
      showQuery: true
    });
    await wrapper.vm.$nextTick();
    const inputDom = wrapper.findComponent(Input);
    expect(inputDom.exists()).toBe(true);
    const clearButton = inputDom.find('.ant-input-clear-icon');
    expect(clearButton.exists()).toBe(true);
    await clearButton.trigger('click');
    expect(inputDom.vm.value).toBe('');
    await wrapper.setProps({
      params: [
        {
          label: '自定义插槽位置',
          slotName: 'customLocation'
        }
      ]
    });
    expect(wrapper.html()).toContain('this is customLocation');
  });
});
