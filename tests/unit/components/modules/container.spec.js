import { mount } from '@vue/test-utils';
import containerPage from '@/components/modules/container.vue';
describe('test containerPage components', () => {
  let wrapper;
  beforeEach(() => {
    wrapper = mount(containerPage, {
      props: {
        noHeader: true,
        isScroll: true
      },
      slots: {
        header: 'this is header',
        content: 'this is content',
        footer: 'this is footer'
      }
    });
  });
  it('test containerPage dom', async () => {
    await wrapper.setProps({
      isScroll: true,
      mainBgColor: 'red'
    });
    expect(wrapper.find('.global-container-header').html()).toContain('this is header');
    expect(wrapper.find('.global-container-content').html()).toContain('this is content');
    await wrapper.setProps({
      isScroll: false
    });
    await wrapper.setProps({
      isScroll: false
    });
    expect(wrapper.find('.global-wrapper-h').html()).toContain('this is footer');
  });
});
