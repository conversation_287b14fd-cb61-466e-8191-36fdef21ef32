import { mount } from '@vue/test-utils';
import paginationPage from '@/components/modules/pagination.vue';
import { Pagination, Select } from 'ant-design-vue';
describe('test pagination components', () => {
  let wrapper;
  beforeEach(() => {
    wrapper = mount(paginationPage, {
      props: {
        page: 1,
        pageSize: 10,
        count: 10,
        pageSizeArr: [10, 20, 40, 60, 80, 100]
      },
      global: {
        components: {
          'a-pagination': Pagination,
          'a-select': Select,
          'a-select-option': Select.Option
        }
      }
    });
  });
  it('test APagination Funcs', async () => {
    const paginationNode = wrapper.findComponent(Pagination);
    expect(paginationNode.exists()).toBeTruthy();
    await paginationNode.vm.$emit('change', 1);
    await paginationNode.vm.$nextTick();
    await paginationNode.vm.$emit('showSizeChange', 1);
    const selectNode = wrapper.findComponent(Select);
    expect(selectNode.exists()).toBeTruthy();
    await selectNode.vm.$emit('update:value', 10);
    expect(selectNode.vm.value).toBe(10);
  });
  it('test pagination dom', async () => {
    await wrapper.setProps({
      page: 0,
      count: 0
    });
    expect(wrapper.find('.page-text').exists()).toBe(true);
    await wrapper.setProps({
      page: 2,
      count: 1,
      pageSize: 1
    });
    expect(wrapper.find('.page-text').exists()).toBe(true);
  });
});
