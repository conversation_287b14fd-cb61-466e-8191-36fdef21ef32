import { mount } from '@vue/test-utils';
import navHeader from '@/components/modules/navHeader.vue';
describe('test navHeader components', () => {
  let wrapper;
  beforeEach(() => {
    wrapper = mount(navHeader, {
      global: {
        directives: {
          debounce: jest.fn(v => v)
        }
      },
      props: {
        title: 'title',
        otherTipsText: 'tips'
      },
      slots: {
        otherText: 'this is otherText',
        otherBtn:'this is otherBtn'
      }
    });
  });
  it('test navHeader dom', async () => {
    await wrapper.setProps({
        functionBtnFn: ()=>{},
    });
    expect(wrapper.html()).toContain('this is otherText');
  });
});
