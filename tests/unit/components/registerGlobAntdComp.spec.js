jest.unmock('ant-design-vue');
import { message } from 'ant-design-vue';
jest.spyOn(message, 'config').mockImplementation(params => {
  if (params.getContainer) {
    params.getContainer();
  }
});
const { componentsPlugin } = require('@/components/registerGlobAntdComp.js');
test('test registerGlobAntdComp file', () => {
  jest.spyOn(document, 'getElementById').mockReturnValue(null);
  const appFn = {
    use: jest.fn()
  };
  componentsPlugin(appFn);
  expect(appFn.use).toHaveBeenCalled();
  expect(message.config).toHaveBeenCalled();
});
