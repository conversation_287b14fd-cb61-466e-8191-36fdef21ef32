import systemApplicationInfo from '@/store/modules/systemApplicationInfo.js';
import { createStore } from 'vuex';
const wechaApplicationsData = {
  data: [{ id: 1, wechatName: '雅戈尔', appTypeName: '公众号', type: 'service', appid: '11111', logo: 'addurl' }]
};
const miniWechaApplicationsData = {
  data: [{ id: 2, wechatName: '潘多拉', appTypeName: '小程序', type: 'miniapp', appid: '2' }]
};
let storeData = {
  applicationList: '[{"wechatName":"雅阁","type":"service","appid":"1"},{"wechatName":"小程序","type":"miniapp","appid":"2"}]',
  currApplicationInfo: '{"wechatName":"雅阁","type":"service","appid":"1"}',
  mail: '<EMAIL>'
};
Object.defineProperty(global, 'sessionStorage', {
  value: {
    getItem: jest.fn(key => storeData[key]),
    setItem: jest.fn(),
    removeItem: jest.fn(),
    clear: jest.fn()
  },
  writable: true
});

jest.mock('@/api/modules/applicationManagement.js', () => ({
  wechaApplications: jest
    .fn()
    .mockImplementation(() => Promise.resolve(wechaApplicationsData))
    .mockResolvedValueOnce({ data: [] }),
  miniWechatList: jest
    .fn()
    .mockImplementation(() => Promise.resolve(miniWechaApplicationsData))
    .mockResolvedValueOnce({ data: [] })
    .mockResolvedValueOnce({ data: [] })
}));

let store = createStore(systemApplicationInfo);
describe('test systemApplicationInfo tool', () => {
  it('test mutations Funcs', () => {
    const currApplicationInfo = { type: 'service', appid: '123' };
    store.commit('SET_CURR_APPLICATION_INFO', currApplicationInfo);
    expect(store.state.currApplicationInfo).toEqual(expect.objectContaining(currApplicationInfo));
    const applicationList = [{ id: 1, wechatName: '雅戈尔', appTypeName: '公众号', type: 'service', appid: '123' }];
    store.commit('SET_APPLICATION_LIST', applicationList);
    expect(store.state.applicationList).toEqual(applicationList);
    store.commit('CLEAR_ALL_SYS_APPLICATION_INFO');
    expect(store.state.applicationList).toEqual([]);
  });
  it('test getApplicationList Funcs and Branch', async () => {
    store.commit('SET_CURR_APPLICATION_INFO', { type: 'service' });
    await store.dispatch('getApplicationList','/applicationManagement');
    store.commit('SET_CURR_APPLICATION_INFO', { type: 'miniapp' });
    await store.dispatch('getApplicationList','/controlManagement');
    await store.dispatch('getApplicationList');
    expect(store.state.applicationList.length).toBeGreaterThan(0);
  });
  it('test defaultCurApplicationData Funcs and Branch', async () => {
    const currApplicationInfo = { wechatName: '小程序', type: 'miniapp', appid: '2' };
    await store.dispatch('defaultCurApplicationData', 'miniapp');
    expect(store.state.currApplicationInfo).toEqual(expect.objectContaining(currApplicationInfo));
    await store.dispatch('defaultCurApplicationData', 'miniappa');
    jest.spyOn(global.sessionStorage, 'getItem').mockReturnValueOnce('').mockReturnValueOnce('');
    await store.dispatch('defaultCurApplicationData', 'miniapp');
  });
});
