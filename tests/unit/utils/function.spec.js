import {  copyProperty,  detectCurrentApplication, downloadFile } from '@/utils/function';
jest.mock('@/axiosConfig/http', () => {
  return {
    get: jest
      .fn(url => {
        return Promise.resolve({ data: 'filedata', url });
      })
      .mockImplementationOnce(() => {
        return Promise.reject({ error: 'error' });
      })
  };
});
jest.mock('@/api/modules/subscriptionMsgManagement', () => {
  return {
    subscriptionMsgManagementApisWhite: {
      list: ['/miniappTemplates/getList', '/miniappTemplates/templateSync'],
      type: 'miniapp'
    }
  };
});
global.window.URL.createObjectURL = jest.fn();
global.window.URL.revokeObjectURL = jest.fn();
global.document = {
  createElement: jest.fn(() => {
    return {
      href: '',
      setAttribute: jest.fn(),
      click: jest.fn(),
      parentNode: {
        removeChild: jest.fn()
      }
    };
  }),
  body: {
    appendChild: jest.fn()
  }
};
jest.mock('@/api/modules/tplMsgManagement', () => {
  return { tplMsgManagementApisWhite: { list: ['/templates', '/custom-templates', '/custom-template-messages'], type: 'service' } };
});
const storeData = {
  currApplicationInfo: '{"type":"miniapp"}',
};
Object.defineProperty(global, 'sessionStorage', {
  value: {
    getItem: jest.fn(v => storeData[v]).mockReturnValueOnce(''),
    setItem: jest.fn(v => v),
    removeItem: jest.fn()
  },
  writable: true
});
describe('test function method ', () => {
  afterEach(() => {
    jest.clearAllMocks();
  });
  it('test copyProperty method', async () => {
    let preData = { a: 1, b: 2, e: 9 },
      next = { a: 3, b: 4, c: 5 };
    let res = copyProperty(preData, next);
    expect(res).toEqual(expect.objectContaining(res));
  });
  it('test downloadFile method', async () => {
    jest.spyOn(console, 'error').mockImplementation(error => error);
    let res = downloadFile('url');
    res = downloadFile('url', 'downloadFile');
  });
  it('test detectCurrentApplication method', async () => {
    let res = detectCurrentApplication('publicPath');
    expect(res).toBe(true);
    res = detectCurrentApplication('/miniappTemplates/getList');
    expect(res).toBe(true);
    res = detectCurrentApplication('/templates');
    expect(res).toBe(false);
    res = detectCurrentApplication('/subList');
    expect(res).toBe(true);
  });
});
