import { debounce } from '@/utils/directive.js';
describe('test directive method ', () => {
  afterEach(() => {
    jest.clearAllMocks();
  });
  it('test debounce method', done => {
    const { beforeMount } = debounce;
    const el = {
      addEventListener: jest.fn((eventName, fn) => {
        return fn && fn();
      }),
      disabled: false
    };
    beforeMount(el, { value: 1000 });
    const elCallFn = el.addEventListener.mock.calls[0][1];
    elCallFn();
    setTimeout(() => {
      beforeMount(el, { value: '' });
      done();
    }, 1000);
    expect(el.addEventListener).toHaveBeenCalled();
  });
});
