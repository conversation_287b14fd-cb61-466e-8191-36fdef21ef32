jest.mock('@/router', () => {
  return {
    beforeEach: jest.fn(),
    push:jest.fn(),
    replace:jest.fn()
  };
});
const { bootstrap, mount, unmount } = require('@/main.js');
let propsFn = {
  isInPortal: jest.fn(() => true),
  getAccessToken: jest.fn(() => Promise.resolve('xxx')),
  getTenantId: jest.fn(() => Promise.resolve('xxx')),
  getUserInfo: jest.fn(() => Promise.resolve({ name: 'linghuazhu', mail: 'xxx' })),
  container: {
    querySelector: jest.fn()
  },
  jumpTo:jest.fn(),
  replaceTo:jest.fn()
};
describe('test main file', () => {
  it('test bootstrap function', async () => {
    await bootstrap();
    await bootstrap(propsFn);
    expect(propsFn.getAccessToken).toHaveBeenCalled();
    jest.spyOn(console, 'error').mockImplementation(error => error);
    propsFn.getAccessToken = jest.fn(() => Promise.reject('404'));
    bootstrap(propsFn);
  });
  it('test unmount function', async () => {
    await unmount();
  });
  it('test mount function', async () => {
    await mount();
    await window.WXAUTH.jumpTo('/home')
    await window.WXAUTH.replaceTo('/about')
    await mount(propsFn);
    expect(propsFn.container.querySelector).toHaveBeenCalled();
  });
});
