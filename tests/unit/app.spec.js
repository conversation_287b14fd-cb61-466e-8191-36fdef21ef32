import { mount } from '@vue/test-utils';
import appPage from '@/App.vue';
import { ConfigProvider } from 'ant-design-vue';
describe('test app page ', () => {
  let wrapper;
  beforeEach(() => {
    wrapper = mount(appPage, {
      global: {
        components: {
          'a-config-provider': ConfigProvider
        },
        stubs: ['router-view']
      }
    });
  });
  it('test table dom', async () => {
    wrapper.vm.isRouterAlive = false;
    await wrapper.vm.$nextTick();
    expect(wrapper.html()).not.toContain('router-view-stub');
  });
});
