import { message } from 'ant-design-vue';
jest.mock('@/router', () => ({
  beforeEach: jest.fn(async fn => {
    await fn(
      {
        params: { id: 1 },
        path: '/home'
      },
      {
        path: '/about'
      },
      jest.fn()
    );
  })
}));
Object.defineProperty(global, 'sessionStorage', {
  value: {
    getItem: jest.fn(key => {
      if (key === 'accessToken') {
        return 'xxx';
      } else if (key === 'applicationList') {
        return '[{"type":"mini"}]';
      }
    }),
    setItem: jest.fn()
  },
  writable: true
});
jest.mock('@/store', () => ({
  dispatch: jest.fn(key => {
    if (key === 'getApplicationList') {
      return Promise.resolve({ data: [] });
    } else if (key === 'defaultCurApplicationData') {
      return {};
    }
  })
}));
require('@/router/routerPromission.js');
const routerBeforeEachCallBack = require('@/router').beforeEach.mock.calls[0][0];
describe('Test the permissions of the routing guard', () => {
  it('Test that the incoming and outgoing routes have the same address', async () => {
    let to = { path: '/home' },
      from = { path: '/home' },
      nextOnce = jest.fn();
    await routerBeforeEachCallBack(to, from, nextOnce);
    expect(nextOnce).toHaveBeenCalled();
  });
  it('Test that the incoming routing address is equal to the offiacount', async () => {
    let to = { path: '/offiaccount' },
      from = { path: '/home' },
      nextTwo = jest.fn();
    await routerBeforeEachCallBack(to, from, nextTwo);
    to = { path: '/miniprogram' };
    await routerBeforeEachCallBack(to, from, nextTwo);
    expect(nextTwo).toHaveBeenCalled();
    global.sessionStorage.getItem.mockImplementationOnce(() => 'asstoken1').mockImplementationOnce(() => '');
    await routerBeforeEachCallBack(to, from, nextTwo);
    global.sessionStorage.getItem.mockImplementationOnce(() => '');
    await routerBeforeEachCallBack(to, from, nextTwo);
    expect(message.error).toBeInstanceOf(Function);
  });
});
