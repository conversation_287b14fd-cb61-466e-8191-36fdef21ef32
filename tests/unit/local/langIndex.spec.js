
jest.unmock('@/local/index');
jest.unmock('vue-i18n');
import {  siphonI18n, usI18n } from '@/local/index';
describe('siphonI18n', () => {
  test('test usI18n function',async () => {
    const appMock = {
        use:jest.fn()
    }
    const result =await usI18n(appMock)
    expect(result).toBe(undefined)
    window.WXAUTH={
        getLang :()=> 'zh-US'
    }
    const result2 =await usI18n(appMock)
    expect(result2).toBe(undefined)
  });
  test('test siphonI18n function',async () => {
    const enModules = require.context(`../../../src/local/lang/en-US`, true, /\.js$/);
    const result =await siphonI18n(enModules,'en-US')
    expect(result).toHaveProperty('applicationManagement');
    const result2 = await siphonI18n(enModules)
    expect(result2).toHaveProperty('common');
  });
});
