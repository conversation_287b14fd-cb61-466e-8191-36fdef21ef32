import http from '@/axiosConfig/http.js';
jest.mock('@/axiosConfig/axios.js', () => {
  return function () {
    return {
      post: jest.fn((url, params, options) => Promise.resolve(params)),
      get: jest.fn((url, params, options) => Promise.resolve(params)),
      put: jest.fn((url, params, options) => Promise.resolve(params)),
      delete: jest.fn((url, params, options) => Promise.resolve(params))
    };
  };
});
jest.mock('@/utils/function.js', () => {
  return {
    detectCurrentApplication: jest.fn(url => {
      if (url == 'publicPath') {
        return true;
      } else {
        return false;
      }
    })
  };
});
let storageData = {
  accessToken: 'token444',
  tenantId: 1,
  appid: 'wxaaappidjfjejj',
  mail: 'xxx'
};
Object.defineProperty(global, 'sessionStorage', {
  value: {
    getItem: jest.fn(v => storageData[v])
  },
  writable: true
});
describe('test http Func and Branches ', () => {
  afterEach(() => {
    jest.clearAllMocks();
  });
  it('test post Funcs', async () => {
    let params = { test: 1 };
    let res = await http.post('publicPath', params, { task: '6666' });
    expect(res).toEqual(expect.objectContaining(params));
    res = await http.post('publicPath', params, { ContentType: '3333' }, { test: '444' });
    expect(res).toEqual(expect.objectContaining(params));
    http.post('other').catch(err => {
      expect(err).toEqual(expect.objectContaining(err));
    });
  });

  it('test get Funcs', async () => {
    let params = { test: 1 };
    let res = await http.get('publicPath', params, { test: 1 }, { token: '3333' });
    expect(res).toEqual(expect.objectContaining({ params }));
    await http.get('publicPath', undefined, { test: 1 });
    http.get('other').catch(err => {
      expect(err).toEqual(expect.objectContaining(err));
    });
  });
  it('test delete Funcs', async () => {
    let params = { test: 1 };
    let res = await http.delete('other', params, { token: '3333' });
    expect(res).toEqual(expect.objectContaining({ params }));
    http.delete('other');
  });
  it('test put Funcs', async () => {
    let params = { test: 1 };
    let res = await http.put('other', params, { taskId: 1 });
    expect(res).toEqual(expect.objectContaining(params));
    await http.put('other');
  });
});
