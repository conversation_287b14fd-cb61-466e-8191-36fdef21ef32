import axiosConfig from '@/axiosConfig/axios.js';
jest.spyOn(console, 'error').mockImplementation(error => error);
jest.mock('axios', () => ({
  defaults: {
    withCredentials: false
  },
  create: jest.fn(() => {
    return {
      interceptors: {
        request: {
          use: jest.fn()
        },
        response: {
          use: jest.fn()
        }
      }
    };
  })
}));
const requestSuccessCallback = axiosConfig().interceptors.request.use.mock.calls[0][0];
const requestFailCallback = axiosConfig().interceptors.request.use.mock.calls[0][1];
const responseSuccessCallback = axiosConfig().interceptors.response.use.mock.calls[0][0];
const responseFailCallback = axiosConfig().interceptors.response.use.mock.calls[0][1];

describe('test axiosConfig ', () => {
  afterEach(() => {
    jest.clearAllMocks();
  });
  it('test config.params requestSuccessCallback', () => {
    let config = { params: { noLoading: true, test: 1 } };
    let requestRes = requestSuccessCallback(config);
    expect(requestRes).toEqual(expect.objectContaining(config));
    config = { data: { noLoading: true, test: 2 } };
    requestRes = requestSuccessCallback(config);
    expect(requestRes).toEqual(expect.objectContaining(config));
    config = { data: { test: 5 } };
    requestRes = requestSuccessCallback(config);
    expect(requestRes).toEqual(expect.objectContaining(config));
    let formData = new FormData();
    formData.append('noLoading', true);
    formData.append('id', 1);
    config = { data: formData };
    requestRes = requestSuccessCallback(config);
    expect(requestRes).toEqual(expect.objectContaining(config));
  });
  it('test error requestFailCallback', () => {
    document.getElementsByClassName = jest.fn(v => {
      return [{ attributes: { style: { value: 'none', display: 'none' } }, style: { display: 'none' } }];
    });
    let config = { stack: 'timeout' };
    requestFailCallback(config).catch(err => {
      expect(err).toEqual(expect.objectContaining(config));
    });
    config = { stack: 'error' };
    requestFailCallback(config).catch(err => {
      expect(err).toEqual(expect.objectContaining(config));
    });
    config = null;
    requestFailCallback(config).catch(err => {
      expect(err).toBeNull();
    });
  });
  it('test responseSuccessCallback', () => {
    const responseArr = [
      { data: { code: 200, errorMessage: 'success' } },
      { data: { errorCode: 0, errorMessage: 'success' } },
      { data: { code: -1, errorMessage: 'fail' }, request: { responseURL: 'publicPath' } },
      { data: { errorCode: -1, errorMessage: '' }, request: { responseURL: 'publicPath' } }
    ];
    for (let i = 0; i < responseArr.length; i++) {
      let error = responseArr[i];
      responseSuccessCallback(error).catch(err => {
        expect(err).toEqual(expect.objectContaining(err));
      });
    }
  });
  it('test responseFailCallback', async () => {
    document.getElementsByClassName = jest.fn(v => {
      return [{ attributes: { style: { value: 'flex', display: 'flex' } }, style: { display: 'flex' } }];
    });
    let errorData = { response: { message: 'fail', status: 400 } };
    responseFailCallback(errorData).catch(err => {
      expect(err).toEqual(expect.objectContaining({ message: 'fail', status: 400 }));
    });
    errorData = { response: { message: 'fail', status: 500, data: { errorMessage: 'error' } } };
    responseFailCallback(errorData).catch(err => {
      expect(err).not.toBe(undefined);
    });
    errorData = { response: { message: 'fail', status: 401 } };
    responseFailCallback(errorData).catch(err => {
      expect(err).not.toBe(undefined);
    });
    errorData = { response: { message: 'fail', status: 801 } };
    responseFailCallback(errorData).catch(err => {
      expect(err).not.toBe(undefined);
    });
  });
});
