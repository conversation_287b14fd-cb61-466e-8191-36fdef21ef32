import { mount } from '@vue/test-utils';
import { defineComponent } from 'vue';
import userAssignmentPage from '@/views/pages/commonModules/userAssignment/index.vue';
import { Button, Table, Select } from 'ant-design-vue';
import updateUser from '@/views/pages/commonModules/userAssignment/dialog/updateUser.vue';
jest.mock('@/views/pages/commonModules/userAssignment/dialog/updateUser.vue', () => ({
  template: '<div class="updateUser-template">this is updateUser</div>'
}));
jest.mock('vuex', () => ({
  useStore: jest.fn(() => {
    return {
      state: {
        systemApplicationInfo: {
          applicationList: [
            { id: 1, wechatName: '雅戈尔', appTypeName: '公众号', type: 'service', createdAt: '2021-09-09' },
            { id: 2, wechatName: '潘多拉', appTypeName: '小程序', type: 'miniapp', createdAt: '2021-09-09' }
          ]
        }
      },
      dispatch: jest.fn()
    };
  })
}));
jest.mock('vue-router', () => ({
  useRoute: jest
    .fn(() => {
      return {
        path: '/cus-wxmsgcenter-fe-web-admin/offiaccount/userAssignment'
      };
    })
}));

jest.mock('@/utils/function.js', () => {
  return {
    downloadFile: () => {}
  };
});
jest.mock('@/views/layout/sysApplicationInfoUtils.js', () => {
  return {
    getValFun: {
      applicationList: () => {
        return [
          {
            id: '11',
            label: 'galassia测试',
            value: '112',
            type: 'service'
          },
          {
            id: '22',
            label: '程序测试',
            value: '221',
            type: 'miniapp'
          }
        ];
      }
    }
  };
});
const userAssignmentList = {
  data: {
    items: [
      {
        loginName: 'linghua.zhu',
        apploacationInfo: 'woaap-kering(GTKJ-XDKJFID-DDDDD-xxxx)，kering-dev(GTKJ-XDKJFID-DDDDD-xxxx)，kering-prod(GTKJ-XDKJFID-DDDDD-33333)，kering-test(GTKJ-XDKJFID-DDDDD-qqqqqq)',
        applyList: [
          { wechatName: 'woaap-kering', brandsId: 'xxxxxx', id: '1' },
          { wechatName: 'kering-dev', brandsId: 'GTKJ-XDKJFID-DDDDD-xxxx', id: '2' },
          { wechatName: 'kering-prod', brandsId: 'GTKJ-XDKJFID-DDDDD-33333', id: '3' },
          { wechatName: 'kering-test', brandsId: 'GTKJ-XDKJFID-DDDDD-qqqqqq', id: '4' }
        ],
        createdDate: '2025-10-09 10:00:00',
        isDelete: false
      },
      {
        loginName: 'linghua.test',
        apploacationInfo: 'woaap-kering(GTKJ-XDKJFID-DDDDD-xxxx)，kering-dev(GTKJ-XDKJFID-DDDDD-xxxx)，kering-prod(GTKJ-XDKJFID-DDDDD-33333)，kering-test(GTKJ-XDKJFID-DDDDD-qqqqqq)',
        applyList: [
          { wechatName: 'woaap-kering', brandsId: 'xxxxxx', id: '1' },
          { wechatName: 'kering-dev', brandsId: 'GTKJ-XDKJFID-DDDDD-xxxx', id: '2' },
          { wechatName: 'kering-prod', brandsId: 'GTKJ-XDKJFID-DDDDD-33333', id: '3' },
          { wechatName: 'kering-test', brandsId: 'GTKJ-XDKJFID-DDDDD-qqqqqq', id: '4' }
        ],
        createdDate: '2025-10-09 10:00:00',
        isDelete: true
      },
      {
        loginName: 'linghua.demo',
        apploacationInfo:
          'woaap-kering(GTKJ-XDKJFID-DDDDD-xxxx)，kering-dev(GTKJ-XDKJFID-DDDDD-xxxx)，kering-prod(GTKJ-XDKJFID-DDDDD-33333)，kering-test(GTKJ-XDKJFID-DDDDD-qqqqqq)，woaap-kering(xxxxxx)，kering-dev(GTKJ-XDKJFID-DDDDD-xxxx)，kering-prod(GTKJ-XDKJFID-DDDDD-33333)',
        applyList: [
          { wechatName: 'woaap-kering', brandsId: 'xxxxxx', id: '1' },
          { wechatName: 'kering-dev', brandsId: 'GTKJ-XDKJFID-DDDDD-xxxx', id: '2' },
          { wechatName: 'kering-prod', brandsId: 'GTKJ-XDKJFID-DDDDD-33333', id: '3' },
          { wechatName: 'kering-test', brandsId: 'GTKJ-XDKJFID-DDDDD-qqqqqq', id: '4' },
          { wechatName: 'kering-testxx', brandsId: 'GTKJ-XDKJFID-DDDDD-qqqqqq', id: '5' }
        ],
        createdDate: '2024-11-09 10:00:00',
        isDelete: false
      }
    ],
    totalCount: 20
  }
};
jest.mock('@/api/modules/tplMsgManagement.js', () => {
  return {
    featchAuthorityList: () => {
      return Promise.resolve(userAssignmentList);
    },
    syncAuthorityList: () => {
      return Promise.resolve({ code: 200 });
    },
    delAuthorityList: () => {
      return Promise.resolve({ code: 200 });
    },
    downloadAuthorityList: '/downloadurl'
  };
});
jest.mock('@/api/modules/applicationManagement.js', () => {
  return {
    featchUserInfoAuthPermission: () => {
      return Promise.resolve({ data: { data: [{ loginName: 'linghua.zhu', disabled: false }] } });
    }
  };
});
jest.mock('@/api/modules/subscriptionMsgManagement.js', () => {
  return {
    miniFeatchAuthorityList: () => {
      return Promise.resolve(userAssignmentList);
    },
    miniSyncAuthorityList: () => {
      return Promise.resolve({ code: 200 });
    },
    miniDelAuthorityList: () => {
      return Promise.resolve({ code: 200 });
    },
    downloadMiniAuthorityList: '/downloadurl'
  };
});
describe('test userAssignmentPage index page', () => {
  let wrapper;
  beforeEach(() => {
    wrapper = mount(userAssignmentPage, {
      global: {
        components: {
          container: defineComponent({ template: '<div><slot name="header"></slot><slot name="content"></slot></div>' }),
          'a-button': Button,
          'a-table': Table,
          updateUser,
          'a-select': Select,
          'a-select-option': Select.Option,
          searchForm: defineComponent({ template: '<div><slot name="apploacationId"></slot><slot name="roleId"></slot></div>' }),
          DownOutlined: defineComponent({ template: '<span></span>' }),
          UpOutlined: defineComponent({ template: '<span></span>' }),
          pagination: defineComponent({ template: '<span></span>' })
        }
      },
      props: {
        operationType: 'offiaccount'
      }
    });
  });
  it('Test user allocation list operation items', () => {
    const linkToNodeWatch = wrapper.findAll('.general-link-to');
    expect(linkToNodeWatch.length).toBeGreaterThan(0);
    linkToNodeWatch.forEach(async el => {
      await el.trigger('click');
    });
    const downOutlinedLink = wrapper.findAll('.anticon-down');
    expect(downOutlinedLink.length).toBeGreaterThan(0);
    downOutlinedLink.forEach(async el => {
      await el.trigger('click');
    });
    const upOutlinedLink = wrapper.findAll('.anticon-up');
    expect(upOutlinedLink.length).toBeGreaterThan(0);
    upOutlinedLink.forEach(async el => {
      await el.trigger('click');
    });
  });
  it('Test and select role data linkage', async () => {
    await wrapper.setProps({
      operationType: 'miniprogram'
    });
    const selectNodeArr = wrapper.findAllComponents(Select);
    expect(selectNodeArr.length).toBeGreaterThan(0);
    selectNodeArr[0].vm.$emit('update:value', '11');
    await selectNodeArr[0].vm.$nextTick();
    expect(selectNodeArr[0].vm.value).toBe('11');
  });
  it('Test adding user mask DOM', async () => {
    wrapper.vm.tableState.updateUserVisible = true;
    await wrapper.vm.$nextTick();
    expect(wrapper.html()).toContain(' class="updateUser-template"');
    const updateUserDom = wrapper.findComponent(updateUser);
    expect(updateUserDom.exists()).toBe(true);
    updateUserDom.vm.$emit('update:updateUserVisible', false);
    await wrapper.vm.$nextTick();
    expect(wrapper.vm.tableState.updateUserVisible).toBe(false);
  });
});
