import { mount } from '@vue/test-utils';
import updateUserPage from '@/views/pages/commonModules/userAssignment/dialog/updateUser.vue';
import { Modal, Select, Button } from 'ant-design-vue';
jest.mock('@/api/modules/applicationManagement.js', () => {
  return {
    featchUserInfoAuthPermission: () => {
      return Promise.resolve({ data: { data: [{ loginName: 'linghua.zhu', disabled: false }] } });
    }
  };
});
jest.mock('@/api/modules/tplMsgManagement.js', () => {
  return {
    updateAuthorityList: () => {
      return Promise.resolve({ code: 200 });
    }
  };
});
jest.mock('@/api/modules/subscriptionMsgManagement.js', () => {
  return {
    miniUpdateAuthorityList: () => {
      return Promise.resolve({ code: 200 });
    }
  };
});
describe('test updateUserPage component page', () => {
  let wrapper;
  beforeEach(() => {
    wrapper = mount(updateUserPage, {
      global: {
        components: {
          'a-modal': Modal,
          'a-select': Select,
          'a-select-option': Select.Option,
          'a-button': Button
        }
      },
      props: {
        updateUserVisible: true,
        operationType: 'offiaccount',
        applicationList: [{ id: 1, label: '应用1' }]
      }
    });
  });
  afterEach(() => {
    jest.clearAllMocks();
  });
  it('test updateUser modal open  Func ', async () => {
    const updateUserNode = wrapper.findComponent(Modal);
    expect(updateUserNode.exists()).toBeTruthy();
    await updateUserNode.vm.$emit('update:open', true);
    expect(updateUserNode.vm.open).toBe(true);
  });
  it('Test selection box results', async () => {
    const selectNode = wrapper.findAllComponents(Select);
    expect(selectNode.length).toBeGreaterThan(0);
    selectNode[0].vm.$emit('update:value', 1);
    await selectNode[0].vm.$nextTick();
    expect(selectNode[0].vm.value).toBe(1);
    selectNode[1].vm.$emit('update:value', 1);
    await selectNode[1].vm.$nextTick();
    expect(selectNode[1].vm.value).toBe(1);
  });
});
