import { PUSH_OF_MA_TABLE_FN } from '@/views/pages/commonModules/tableColumnsConfig.js';
describe('test function method ', () => {
  afterEach(() => {
    jest.clearAllMocks();
  });
  it('test PUSH_OF_MA_TABLE_FN method', async () => {
    let res = PUSH_OF_MA_TABLE_FN('offiaccount');
    expect(res.length).toBeGreaterThan(0);
    res = PUSH_OF_MA_TABLE_FN('miniprogram');
    expect(res.length).toBeGreaterThan(5);
  });
});
