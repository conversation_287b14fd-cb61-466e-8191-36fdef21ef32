import { mount } from '@vue/test-utils';
import controlManagement from '@/views/pages/commonModules/controlManagement/index.vue';
import { Button, Table } from 'ant-design-vue';
import container from '@/components/modules/container.vue';
const suppliersList ={
  data: [
    {
      id: 1,
      name: '标题1',
      contacts: 'ling',
      status: 1,
      tel: '187cccc1234',
      appid: 'xxee',
      appkey: 'xxx',
      useCallTime: '2023-09-09',
      useTimeByToday: '3'
    },
    {
      id: 2,
      name: '标题1',
      contacts: 'hua',
      status: 0,
      tel: '187cccc1234',
      appid: 'xxee',
      appkey: 'xxx',
      useCallTime: '2023-09-09',
      useTimeByToday: '3'
    }
  ]
}
jest.mock('@/api/modules/controlManagement.js', () => ({
  suppliersList: jest.fn(() =>
    Promise.resolve(suppliersList)
  ),
  deleteSuppliers: jest.fn(),
  miniSuppliersList: jest.fn(() =>
    Promise.resolve(suppliersList)
  ),
  miniDeleteSuppliers: jest.fn()
}));
describe('test controlManagement index page', () => {
  let wrapper;
  beforeEach(() => {
    wrapper = mount(controlManagement, {
      props: {
        title: 'Central control management',
        operationType: 'offiaccount'
      },
      global: {
        components: {
          container,
          'a-button': Button,
          'a-table': Table
        },
        stubs:['navHeader']
      }
    });
  });
  afterEach(() => {
    jest.clearAllMocks();
  });
  it('Test the operation methods in the table', async () => {
    const linkToNodeOper = wrapper.findAll('.general-link-to');
    expect(linkToNodeOper.length).toBeGreaterThan(0);
    linkToNodeOper.forEach(async (el, index) => {
      await el.trigger('click');
    });
  });
});
