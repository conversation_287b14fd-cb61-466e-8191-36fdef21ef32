import { mount } from '@vue/test-utils';
import updateSuppliersForm from '@/views/pages/commonModules/controlManagement/updateSuppliersForm.vue';
import container from '@/components/modules/container.vue';
import { DatePicker, Form, Input, Button, Radio, Checkbox ,Modal} from 'ant-design-vue';
const getApisList = {
  data: [
    { name: 'name one', id: 1 },
    { name: 'name two', id: 2 }
  ]
}
const detailData = {
  data: {
    domain: [{ url: 'path' }],
    apiList: [1],
    name: 'name',
    contacts: 'ling',
    tel: '1878xxxty78',
    checkIp: 1,
    ip: 'public',
    checkExp: true,
    extAt: '2023-09-09'
  }
}
jest.mock('@/api/modules/controlManagement.js', () => {
  return {
    getApis: jest.fn(() =>
      Promise.resolve(getApisList)
    ),
    detail: jest.fn(() =>
      Promise.resolve(detailData)
    ),
    createSuppliersForm: jest.fn(),
    updateSuppliersForm: jest.fn(),
    miniGetApis: jest.fn(() =>
      Promise.resolve(getApisList)
    ),
    miniDetail: jest.fn(() =>
      Promise.resolve(detailData)
    ),
    miniCreateSuppliersForm: jest.fn(),
    miniUpdateSuppliersForm: jest.fn()
  };
});
jest.mock('@/utils/function', () => ({
  copyProperty: jest.fn((source, data) => data)
}));
describe('test updateSuppliersForm page', () => {
  let wrapper;
  beforeEach(() => {
    wrapper = mount(updateSuppliersForm, {
      global: {
        components: {
          container,
          'a-form': Form,
          'a-form-item': Form.Item,
          'a-input': Input,
          'a-input-group': Input.Group,
          'a-radio-group': Radio.Group,
          'a-radio-button': Radio.Button,
          'a-button': Button,
          'a-checkbox': Checkbox,
          'a-checkbox-group': Checkbox.Group,
          'a-date-picker': DatePicker,
          'a-modal':Modal
        },
        stubs:['navHeader'],
        directives: {
          debounce: jest.fn(v => v)
        }
      }
    });
  });
  afterEach(() => {
    jest.clearAllMocks();
  });
  it('test form inputs Func and Branch ', async () => {
    wrapper.vm.formData.authorizedDomainList=[{url:''}]
    await wrapper.vm.$nextTick();
    const supplierInputNodeArr = wrapper.findAllComponents(Input);
    expect(supplierInputNodeArr.length).toBeGreaterThan(0);
    supplierInputNodeArr.forEach(async (el, index) => {
      await el.vm.$emit('update:value', index);
      await el.vm.$nextTick();
      expect(el.vm.value).toEqual(index);
    });
  });
  it('test form radio Func and Branch ', async () => {
    const supplierRadioNodeArr = wrapper.findAllComponents(Radio.Group);
    expect(supplierRadioNodeArr.length).toBeGreaterThan(0);
    supplierRadioNodeArr.forEach(async el => {
      await el.vm.$emit('update:value', 1);
      await el.vm.$nextTick();
      expect(el.vm.value).toEqual(1);
    });
  });
  it('test form datePicker Func and Branch ', async () => {
    const supplieDateNodeArr = wrapper.findComponent(DatePicker);
    expect(supplieDateNodeArr.exists()).toBeTruthy();
    await supplieDateNodeArr.vm.$emit('update:value', '2023-09-09');
    await supplieDateNodeArr.vm.$nextTick();
    expect(supplieDateNodeArr.vm.value).toEqual('2023-09-09');
  });
  it('test checkbox Func and Branch',async()=>{
    const checkboxNode = wrapper.findComponent(Checkbox);
    expect(checkboxNode.exists()).toBeTruthy();
    await checkboxNode.vm.$emit('update:value', true);
    const checkboxNodeArr = wrapper.findAllComponents(Checkbox.Group);
    expect(checkboxNodeArr.length).toBeGreaterThan(0);
    await checkboxNodeArr[0].vm.$emit('update:value', [1])
    expect(checkboxNodeArr[0].vm.value).toEqual([1]);
  })
  it('test modal form Func and Branch',async()=>{
    wrapper.vm.formData.createSuccessTipShow=true
    await wrapper.vm.$nextTick();
    const modalNode = wrapper.findComponent(Modal)
    expect(modalNode.exists()).toBeTruthy();
    const modalCheckBoxNode = modalNode.findComponent(Checkbox)
    expect(modalCheckBoxNode.exists()).toBeTruthy();
  })
});
