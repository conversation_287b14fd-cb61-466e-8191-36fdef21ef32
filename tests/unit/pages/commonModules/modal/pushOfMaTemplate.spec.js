import { mount } from '@vue/test-utils';
import pushOfMaTemplate from '@/views/pages/commonModules/modal/pushOfMaTemplate.vue';
import { Table, Button } from 'ant-design-vue';
import container from '@/components/modules/container.vue';
const sendResultList = [
  {
    appid: 1,
    templateName: '标题1',
    templateId: 'ling',
    openid: 1,
    id: '187cccc1234',
    failMassage: '成功',
    appid: '2023-09-09',
    unionid: '3',
    status: '1'
  },
  {
    appid: 1,
    templateName: '标题1',
    templateId: 'ling',
    openid: 1,
    id: '187cccc12341',
    failMassage: '成功',
    appid: '2023-09-09',
    unionid: '3',
    status: ''
  }
];
jest.mock('@/api/modules/subscriptionMsgManagement.js', () => ({
  maSendResultList: jest.fn(() =>
    Promise.resolve({
      data: {
        list: sendResultList,
        totalCount: 2
      }
    })
  ),
  maSendResultListDownload: 'publicPath',
  miniMessageChannelList:jest.fn(() =>
    Promise.resolve({
      data:['me']
    })
  ),
}));
jest.mock('@/api/modules/tplMsgManagement.js', () => ({
  offMaSendResultList: jest.fn(() =>
    Promise.resolve({
      data: {
        list: sendResultList,
        totalCount: 2
      }
    })
  ),
  offMaSendResultListDownload: 'publicPathTest',
  messageChannelList:jest.fn(() =>
    Promise.resolve({
      data:['ms']
    })
  ),
}));
jest.mock('@/views/pages/commonModules/tableColumnsConfig.js', () => {
  return {
    PUSH_OF_MA_TABLE_FN: () => {
      return [
        {
          title: '模板名称',
          dataIndex: 'templateName',
          key: 'templateName'
        },
        {
          title: '发送状态',
          dataIndex: 'status',
          key: 'status'
        }
      ];
    }
  };
});
jest.mock('@/utils/function.js', () => {
  return {
    downloadFile: () => {}
  };
});
describe('test pushOfMaTemplate index page', () => {
  let wrapper;
  beforeEach(() => {
    wrapper = mount(pushOfMaTemplate, {
      global: {
        components: {
          container,
          'a-table': Table,
          'a-button': Button
        },
        stubs: ['searchForm', 'pagination']
      },
      props: {
        operationType: 'offiaccount'
      }
    });
  });
  afterEach(() => {
    jest.clearAllMocks();
  });
  it('Test the operation methods in the table', async () => {
    expect(wrapper.html()).toContain('class="general-mn"');
  });
});
