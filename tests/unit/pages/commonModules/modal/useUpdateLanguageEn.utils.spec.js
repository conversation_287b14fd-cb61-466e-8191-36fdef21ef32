import { useUpdateLanguageEnFn } from '@/views/pages/commonModules/modal/useUpdateLanguageEn.utils.js';
describe('test useUpdateLanguageEnFn  method', () => {
  it('test updateTemplateLanguage method ', async () => {
    const { updateTemplateLanguage, updateLanguageState } = useUpdateLanguageEnFn();
    let rowData = {
      id: 1,
      title: '标题1',
      templateId: 11,
      status: false,
      modifiedDate: '2024-09-09',
      contentEn: 'start{{first.DATA}}\nlevel{{keyword1.DATA}}\ninfo{{keyword2.DATA}}\nremark{{remark.DATA}}\n{{',
      content: '{{first.DATA}}\n错误级别：{{keyword1.DATA}}\n错误信息：{{keyword2.DATA}}\n{{remark.DATA}}'
    };
    await updateTemplateLanguage(rowData);
    rowData.contentEn = '';
    await updateTemplateLanguage(rowData);
    expect(updateLanguageState.updateLanguageVisible).toBe(true);
  });
});
