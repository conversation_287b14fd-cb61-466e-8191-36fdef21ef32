import { mount } from '@vue/test-utils';
import updateLanguageEn from '@/views/pages/commonModules/modal/updateLanguageEn.vue';
import { Modal, Form, Button, Input } from 'ant-design-vue';
jest.mock('@/api/modules/tplMsgManagement.js', () => {
  return {
    templatesEdit: jest.fn(),
    downloadTemplate: jest.fn(() => 'publicPath')
  };
});
jest.mock('@/api/modules/subscriptionMsgManagement.js', () => {
  return {
    miniappTemplatesEdit: jest.fn()
  };
});
describe('test updateLanguageEn  page', () => {
  let wrapper;
  beforeEach(() => {
    wrapper = mount(updateLanguageEn, {
      global: {
        components: {
          'a-modal': Modal,
          'a-form': Form,
          'a-form-item': Form.Item,
          'a-button': Button,
          'a-input': Input
        }
      },
      props: {
        updateLanguageVisible: true,
        updateLanguageFormFieldList: [
          {
            value: 'null1',
            label: 'orderNo',
            key: 'orderId'
          },
          {
            value: 'null1',
            key: 'test'
          }
        ]
      }
    });
  });
  afterEach(() => {
    jest.clearAllMocks();
  });
  it('test modal open  Func ', async () => {
    const modalNodes = wrapper.findComponent(Modal);
    expect(modalNodes.exists()).toBeTruthy();
    await modalNodes.vm.$emit('update:open', true);
    expect(modalNodes.vm.open).toBe(true);
  });
  it('test form  Func ', async () => {
    const inputNodeArrs = wrapper.findAllComponents(Input);
    expect(inputNodeArrs.length).toBeGreaterThan(0);
    inputNodeArrs.forEach(async (el, index) => {
      await el.vm.$emit('update:value', index);
      await el.vm.$nextTick();
      expect(el.vm.value).toEqual(index);
    });
  });
});
