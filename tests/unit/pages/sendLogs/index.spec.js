import { mount } from '@vue/test-utils';
import sendLogs from '@/views/pages/sendLogs/index.vue';
import { Table } from 'ant-design-vue';
import container from '@/components/modules/container.vue';
jest.mock('@/api/modules/sendLogs.js', () => ({
  recordList: jest.fn(() =>
    Promise.resolve({
      data: {
        items: [
          {
            id: 1,
            templateTitle: '标题1',
            templateType: 'ling',
            msgType: 1,
            templateId: '187cccc1234',
            sendStatus: '成功',
            sendTime: '2023-09-09',
            total: '3'
          },
          {
            id: 2,
            templateTitle: '标题1',
            templateType: 'hua',
            msgType: 2,
            templateId: '187cccc1234',
            sendStatus: '失败',
            sendTime: '2023-09-09',
            total: '3'
          }
        ],
        totalCount: 2
      }
    })
  ),
  downloadApi: 'publicPath'
}));
describe('test sendLogs index page', () => {
  let wrapper;
  beforeEach(() => {
    wrapper = mount(sendLogs, {
      global: {
        components: {
          container,
          'a-table': Table
        },
        stubs: ['searchForm', 'pagination', 'navHeader']
      }
    });
    jest.spyOn(window, 'open').mockImplementation(() => 'ok');
  });
  afterEach(() => {
    jest.clearAllMocks();
  });
  it('Test the operation methods in the table', async () => {
    jest.spyOn(window, 'open').mockImplementation(() => 'ok');
    const sendlinkToNodeOper = wrapper.findAll('.general-link-to');
    expect(sendlinkToNodeOper.length).toBeGreaterThan(0);
    sendlinkToNodeOper.forEach(async el => {
      await el.trigger('click');
    });
  });
});
