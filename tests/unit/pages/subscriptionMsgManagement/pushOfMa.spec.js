import { mount } from '@vue/test-utils';
import pushOfMa from '@/views/pages/subscriptionMsgManagement/pushOfMa.vue';
import pushOfMaTemplate from '@/views/pages/commonModules/modal/pushOfMaTemplate.vue';
jest.mock('@/views/pages/commonModules/modal/pushOfMaTemplate.vue', () => ({
  template: '<div class="pushOfMa-Template">this is pushOfMaTemplate</div>'
}));
describe('test pushOfMa index page', () => {
  let wrapper;
  beforeEach(() => {
    wrapper = mount(pushOfMa, {
      global: {
        components: {
          pushOfMaTemplate,
        },
      }
    });
  });
  afterEach(() => {
    jest.clearAllMocks();
  });
  it('Test pushOfMa module', async () => {
    expect(wrapper.html()).toContain('class="pushOfMa-Template"');
  });
});
