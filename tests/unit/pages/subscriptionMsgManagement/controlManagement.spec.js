import { mount } from '@vue/test-utils';
import miniControlManagement from '@/views/pages/subscriptionMsgManagement/controlManagement.vue';
import controlManagement from '@/views/pages/commonModules/controlManagement/index.vue';
jest.mock('@/views/pages/commonModules/controlManagement/index.vue', () => ({
  template: '<div class="controlManagement-Template">this is controlManagement</div>'
}));
describe('test miniControlManagement  page', () => {
  let wrapper;
  beforeEach(() => {
    wrapper = mount(miniControlManagement, {
      global: {
        components: {
          controlManagement
        }
      }
    });
  });

  it('test miniControlManagement page  dom', () => {
    expect(wrapper.html()).toContain('controlManagement-Template');
  });
});
