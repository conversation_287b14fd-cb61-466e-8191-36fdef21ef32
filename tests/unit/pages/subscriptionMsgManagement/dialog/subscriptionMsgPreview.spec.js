import subscriptionMsgPreview from '@/views/pages/subscriptionMsgManagement/dialog/subscriptionMsgPreview.vue';
import { mount } from '@vue/test-utils';
import { Modal, Button } from 'ant-design-vue';
jest.mock('@/api/modules/subscriptionMsgManagement.js', () => {
  return {
    miniappCustomTemplatesPreview: jest.fn()
  };
});
describe('test sending subscription message template preview', () => {
  let wrapper;
  beforeEach(() => {
    wrapper = mount(subscriptionMsgPreview, {
      global: {
        components: {
          'a-modal': Modal,
          'a-button': Button,
        },
        stubs:['a-textarea']
      },
      props: {
        subscriptionMsgPreview: true,
        curPlanId: 1
      }
    });
  });
  afterEach(() => {
    jest.clearAllMocks();
  });
  it('test sending preview DOM', async () => {
    await wrapper.vm.$nextTick();
    const msgModalNode = wrapper.findComponent(Modal);
    expect(msgModalNode.exists()).toBeTruthy();
    await msgModalNode.vm.$emit('update:open', true);
    expect(msgModalNode.vm.open).toBe(true);
  });
});
