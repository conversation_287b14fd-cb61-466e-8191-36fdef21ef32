jest.unmock('ant-design-vue');
import { mount } from '@vue/test-utils';
import templateDetailPage from '@/views/pages/subscriptionMsgManagement/dialog/templateDetail.vue';
import { Modal,Image } from 'ant-design-vue';
jest.mock('@/views/layout/sysApplicationInfoUtils.js',()=>{
    return{
        getValFun:{
            currApplicationInfo:()=>{
                return {
                    label:'label',
                    logo:'logo'
                }
            }
        }
    }
})
describe('test templateDetailPage index page', () => {
  let wrapper;
  beforeEach(() => {
    wrapper = mount(templateDetailPage, {
      global: {
        components: {
          'a-modal': Modal,
          'a-image':Image
        }
      },
      props: {
        timplateDetailVisible: true,
        templateDetailInfo: {
          isTemplateHidden:true,
          title:'title',
          example:['1','2'],
          templateId:'1',
          content:['q']
        }
      }
    });
  });
  afterEach(() => {
    jest.clearAllMocks();
  });
  it('test template modal open  Func ', async () => {
    await wrapper.vm.$nextTick();
    const tempModalNode = wrapper.findComponent(Modal);
    expect(tempModalNode.exists()).toBeTruthy();
    await tempModalNode.vm.$emit('update:open', true);
    expect(tempModalNode.vm.open).toBe(true);
    await tempModalNode.vm.$nextTick();
    wrapper.setProps({
      timplateDetailVisible: true,
      templateDetailInfo: {
        isTemplateHidden:false,
        example:['1','2'],
      }
    });
    await wrapper.vm.$nextTick();
    wrapper.setProps({
        timplateDetailVisible: false,
        templateDetailInfo:null
      });
  });
});
