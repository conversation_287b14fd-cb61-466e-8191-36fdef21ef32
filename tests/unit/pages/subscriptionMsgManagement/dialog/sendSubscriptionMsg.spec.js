import { mount } from '@vue/test-utils';
import sendSubscriptionMsg from '@/views/pages/subscriptionMsgManagement/dialog/sendSubscriptionMsg.vue';
import { Modal, Select, Form, Button, Upload, DatePicker, Segmented } from 'ant-design-vue';
jest.mock('@/api/modules/subscriptionMsgManagement.js', () => {
  return {
    miniappCustomTemplatesSend: jest.fn(),
    downloadTemplateFile: jest.fn(() => 'publicPath')
  };
});
jest.mock('@/utils/function.js', () => ({
  downloadFile: jest.fn()
}));
describe('test sendSubscriptionMsg index page', () => {
  let wrapper;
  beforeEach(() => {
    wrapper = mount(sendSubscriptionMsg, {
      global: {
        components: {
          'a-modal': Modal,
          'a-form': Form,
          'a-form-item': Form.Item,
          'a-select': Select,
          'a-select-option': Select.Option,
          'a-upload': Upload,
          'a-button': Button,
          'a-date-picker': DatePicker,
          'a-segmented': Segmented,
          'a-range-picker': DatePicker.RangePicker
        }
      },
      props: {
        sendSubscriptionMsgVisible: true,
        curPlanId: 1
      }
    });
  });
  afterEach(() => {
    jest.clearAllMocks();
  });
  it('test send modal open  Func ', async () => {
    const sendModalNode = wrapper.findComponent(Modal);
    expect(sendModalNode.exists()).toBeTruthy();
    await sendModalNode.vm.$emit('update:open', true);
    expect(sendModalNode.vm.open).toBe(true);
  });
  it('test segmented modal open  Func ', async () => {
    const segmentedMode = wrapper.findComponent(Segmented);
    expect(segmentedMode.exists()).toBeTruthy();
    await segmentedMode.vm.$emit('update:value', 1);
    expect(segmentedMode.vm.value).toBe(1);
  });
  it('test form select modal ', async () => {
    const sendSelectNode = wrapper.findAllComponents(Select);
    expect(sendSelectNode.length).toBeGreaterThan(0);
    sendSelectNode.forEach(async (el, index) => {
      await el.vm.$emit('update:value', index);
      expect(el.vm.value).toEqual(index);
    });
  });
  it('test form date modal ', async () => {
    expect(wrapper.vm).toBeDefined();
    wrapper.vm.formData.formItemData.uploadType = 3;
    wrapper.vm.formData.formItemData.isPlan = 1;
    await wrapper.vm.$nextTick();
    const dateNode = wrapper.findComponent(DatePicker);
    expect(dateNode.exists()).toBeTruthy();
    await dateNode.vm.$emit('update:value', '2021-09-09 10:10:00');
    expect(dateNode.vm.value).toBe('2021-09-09 10:10:00');
    const rangePickerNode = wrapper.findComponent(DatePicker.RangePicker);
    expect(rangePickerNode.exists()).toBeTruthy();
    const rangeValue = ['2021-09-09 10:10:00', '2021-09-10 10:10:00'];
    await rangePickerNode.vm.$emit('update:value', rangeValue);
    expect(rangePickerNode.vm.value).toEqual(rangeValue);
  });
  it('test upload Func', async () => {
    expect(wrapper.vm).toBeDefined();
    wrapper.vm.formData.formItemData.uploadType = 1;
    await wrapper.vm.$nextTick();
    const uploadNode = wrapper.findComponent(Upload);
    expect(uploadNode.exists()).toBeTruthy();
    uploadNode.vm.$emit('beforeUpload');
  });
});
