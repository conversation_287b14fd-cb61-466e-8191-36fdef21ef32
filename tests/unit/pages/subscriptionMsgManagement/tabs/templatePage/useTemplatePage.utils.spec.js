import { Modal } from 'ant-design-vue';
jest.mock('@/api/modules/subscriptionMsgManagement.js', () => {
  return {
    miniappTemplatesList: jest.fn(() =>
      Promise.resolve({
        data: {
          list: [],
          totalCount: '10'
        }
      })
    ).mockResolvedValueOnce({data:{list:'',totalCount:10}}),
    miniappTemplatesCheckTemplate: jest.fn(() => Promise.resolve({}))
  };
});
global.window.WXAUTH = {
  jumpTo: jest.fn()
};
jest.spyOn(Modal, 'confirm').mockImplementation(params => {
  if (params.onCancel) {
    params.onCancel();
  }
  if (params.onOk) {
    params.onOk();
  }
});
const { useTemplatePageFn } = require('@/views/pages/subscriptionMsgManagement/tabs/templatePage/useTemplatePage.utils.js');
describe('test the useTemplate PageFn method', () => {
  it('test getMiniappTemplatesList method', async () => {
    const { getMiniappTemplatesList, tableState } = useTemplatePageFn();
    await getMiniappTemplatesList();
    await getMiniappTemplatesList(true);
    expect(tableState.pageInfo.count).toEqual(10);
  });
  it('test gosubscribe method', async () => {
    const { gosubscribe } = useTemplatePageFn();
    gosubscribe();
  });
  it('test checkTemplate method', async () => {
    const { checkTemplate } = useTemplatePageFn();
    await checkTemplate(1);
  });
  it('test getDetail method', async () => {
    const { getDetail, tableState } = useTemplatePageFn();
    const params = {
      example: '',
      content: '\n test{{test}}',
      templateId: 1,
      title: 'title'
    };
    getDetail(params);
    expect(tableState.timplateDetailVisible).toBe(true);
  });
  it('test getPage method', async () => {
    const { getPage } = useTemplatePageFn();
    getPage(1,10);
    getPage();
  });
  it('test searchEvent method', async () => {
    const { searchEvent } = useTemplatePageFn();
    searchEvent();
  });
  it('test synchronization method', async () => {
    const { synchronization } = useTemplatePageFn();
    await synchronization();
  });
});
