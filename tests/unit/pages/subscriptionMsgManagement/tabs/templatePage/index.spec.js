import { mount } from '@vue/test-utils';
import templatePageIndex from '@/views/pages/subscriptionMsgManagement/tabs/templatePage/index.vue';
import { But<PERSON>, Table } from 'ant-design-vue';
import updateLanguageEn from '@/views/pages/commonModules/modal/updateLanguageEn.vue';
import templateDetail from '@/views/pages/subscriptionMsgManagement/dialog/templateDetail.vue';
jest.mock('@/components/modules/searchForm/index.vue', () => ({
  template: '<div class="search-Template">this is searchForm</div>'
}));
jest.mock('@/components/modules/pagination.vue', () => ({
  template: '<div class="pagination-Template">this is pagination</div>'
}));
jest.mock('@/views/pages/commonModules/modal/updateLanguageEn.vue', () => ({
  template: '<div class="language-Template">this is updateLanguageEn</div>'
}));
jest.mock('@/views/pages/subscriptionMsgManagement/dialog/templateDetail.vue', () => ({
  template: '<div class="detail-Template">this is templateDetail</div>'
}));
jest.mock('@/views/pages/commonModules/modal/useUpdateLanguageEn.utils.js', () => {
  return {
    useUpdateLanguageEnFn: jest
      .fn(() => {
        return {
          updateLanguageState: {
            updateLanguageVisible: true,
            updateLanguageId: 1,
            updateLanguageFormFieldList: []
          },
          updateTemplateLanguage: jest.fn()
        };
      })
      .mockReturnValueOnce({
        updateLanguageState: {
          updateLanguageVisible: false,
          updateLanguageId: 1,
          updateLanguageFormFieldList: []
        },
        updateTemplateLanguage: jest.fn()
      })
  };
});
jest.mock('@/views/pages/subscriptionMsgManagement/tabs/templatePage/useTemplatePage.utils.js', () => {
  return {
    useTemplatePageFn:()=>{
        return {
            getMiniappTemplatesList:jest.fn(),
            filterState:{
                filterCriteriaList:[],
                filterParams:{}
            },
            tableState:{
                templateList:[
                    {status:1,type:0,id:1},
                    {status:0,type:8,id:2}
                ],
                columns:[
                    {
                        title: '序号',
                        dataIndex: 'id',
                        key: 'id'
                      },
                      {
                        title: '模板类型',
                        dataIndex: 'type',
                        key: 'type'
                      },
                      {
                        title: '状态',
                        dataIndex: 'status',
                        key: 'status'
                      },
                      {
                        title: '操作',
                        dataIndex: 'operation',
                        key: 'operation'
                      }
                ],
                pageInfo:{
                    pageSize:10,
                    pageNumber:1,
                    count:0
                },
                timplateDetailVisible:true,
                templateDetailInfo:null,
            
            },
            searchEvent:jest.fn(),
            synchronization:jest.fn(),
            getDetail:jest.fn(),
            checkTemplate:jest.fn(),
            gosubscribe:jest.fn(),
            getPage:jest.fn(),
        }
    }
  };
});
describe('test templatePageIndex index page', () => {
  let wrapper;
  beforeEach(() => {
    wrapper = mount(templatePageIndex, {
      global: {
        components: {
          'a-button': Button,
          'a-table': Table,
          templateDetail
        },
        stubs: {
          updateLanguageEn,
          searchForm: true,
          pagination: true
        }
      }
    });
  });
  it('test page linkto Func', async () => {
    const linkToNodeMini = wrapper.findAll('.general-link-to');
    expect(linkToNodeMini.length).toBeGreaterThan(0);
    linkToNodeMini.forEach(async (el, index) => {
      await el.trigger('click');
    });
  });
  it('test updateLanguageEn component Func', async () => {
    const updateLanguageEnMiniNode = wrapper.findComponent(updateLanguageEn);
    expect(updateLanguageEnMiniNode.exists()).toBeTruthy();
    updateLanguageEnMiniNode.vm.$emit('update:updateLanguageVisible', false);
  });
  it('test templateDetail component Func', async () => {
    const linkToNodeTempMini = wrapper.findAll('.general-link-to');
    expect(linkToNodeTempMini.length).toBeGreaterThan(0);
    await linkToNodeTempMini[0].trigger('click');
    await wrapper.vm.$nextTick();
    const templateDetailMiniNode = wrapper.findComponent(templateDetail);
    expect(templateDetailMiniNode.exists()).toBeTruthy();
    templateDetailMiniNode.vm.$emit('update:templateDetailVisible', false);
  });
});
