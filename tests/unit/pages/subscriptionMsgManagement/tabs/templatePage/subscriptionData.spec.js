import { mount } from '@vue/test-utils';
import subscriptionData from '@/views/pages/subscriptionMsgManagement/tabs/templatePage/subscriptionData.vue';
import { Table, Button } from 'ant-design-vue';
import { useRoute } from 'vue-router';
import container from '@/components/modules/container.vue';
jest.mock('@/components/modules/searchForm/index.vue', () => ({
  template: '<div class="searchform-template">this is searchform template <slot name="right"></></div>'
}));
jest.mock('@/utils/function.js',()=>({
  downloadFile:jest.fn()
}))
global.window.AUTH = {
  replaceTo: jest.fn()
};
useRoute.mockReturnValueOnce({
  query: {
    id: '1'
  }
});
jest.mock('@/api/modules/subscriptionMsgManagement.js', () => {
  return {
    miniappTemplatesUserList: jest.fn(() =>
      Promise.resolve({
        data: {
          list:  [
            {isUse:1 ,id: 1, subscribeType: 'accept',},
            { isUse:8 ,id: 2,  subscribeType: ''},
          ],
          totalCount: 3
        }
      })
    ),
    miniappTemplatesDownload: jest.fn(() => 'other'),
  };
});
describe('test subscriptionData index page', () => {
  let wrapper;
  beforeEach(() => {
    wrapper = mount(subscriptionData, {
      global: {
        components: {
          container,
          'a-table': Table,
          'a-button': Button,
        },
        stubs: ['pagination', 'navHeader', 'searchForm']
      }
    });
  });
 
  it('test downloadSubscriptionData  Func ', async () => {
    const downloadSubscriptionDataNode = wrapper.find('.ant-btn-background-ghost');
    expect(downloadSubscriptionDataNode.exists()).toBe(true);
    downloadSubscriptionDataNode.trigger('click');
  });
});
