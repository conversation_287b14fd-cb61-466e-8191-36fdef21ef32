import apis from '@/api/modules/subscriptionMsgManagement.js';
import { Modal } from 'ant-design-vue';
jest.mock('@/api/modules/subscriptionMsgManagement.js', () => {
  return {
    miniappCustomTemplatesPreview: jest.fn(),
    miniappCustomTemplatesDel: jest.fn(),
    templatePlansList: jest
      .fn(() =>
        Promise.resolve({
          data: {
            totalCount: '1',
            list: [
              {
                id: '2344444',
                templateNo: '222xxx',
                name: '活动促销',
                createdDate: '2024-07-17 16:04:28'
              }
            ]
          }
        })
      )
      .mockResolvedValueOnce({ data: { list: null, totalCount: 0 } })
  };
});
global.window.WXAUTH = {
  jumpTo: jest.fn()
};
jest.spyOn(Modal, 'confirm').mockImplementation(params => {
  if (params.onCancel) {
    params.onCancel();
  }
  if (params.onOk) {
    params.onOk();
  }
});
const { useNewPageFn } = require('@/views/pages/subscriptionMsgManagement/tabs/newsPage/useNewPage.utils.js');
describe('test the newsPage.utls.js file', () => {
  afterEach(() => {
    jest.clearAllMocks();
  });
  it('test the getNewPageList method', async () => {
    const { tableState, getNewPageList, filterState } = useNewPageFn();
    await getNewPageList();
    filterState.filterParams.searchTimeStart = '2023-09-09';
    filterState.filterParams.searchTimeEnd = '2023-10-19';
    await getNewPageList(true);
    expect(tableState.newsList.length).toBe(1);
  });
  it('test the searchEvent method ', async () => {
    const { searchEvent, tableState } = useNewPageFn();
    const queryPar = { name: 'ling' };
    await searchEvent(queryPar);
    expect(tableState.newsList.length).toBe(1);
  });
  it('test the getPage method ', async () => {
    const { getPage, tableState } = useNewPageFn();
    await getPage(1);
    await getPage(1, 10);
    expect(tableState.pageInfo.pageNumber).toBe(1);
  });
  it('test the updateMiniTemplateForm  method', () => {
    const { updateMiniTemplateForm } = useNewPageFn();
    updateMiniTemplateForm({ id: '' });
    updateMiniTemplateForm({ id: 1 });
  });
  it('test the delateMiniTemplate method', async () => {
    const { delateMiniTemplate } = useNewPageFn();
    await delateMiniTemplate(1);
    expect(apis.miniappCustomTemplatesDel).toHaveBeenCalled();
  });
  it('test the checkDetail method', async () => {
    const { checkDetail } = useNewPageFn();
    await checkDetail({ templates: { content: '', title: 'title' }, templateId: 1 });
  });
  it('test the lookSendRecords method', async () => {
    const { lookSendRecords } = useNewPageFn();
    await lookSendRecords(1);
    expect(window.WXAUTH.jumpTo).toHaveBeenCalled();
  });
});
