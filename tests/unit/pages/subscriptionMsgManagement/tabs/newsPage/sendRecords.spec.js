import { mount } from '@vue/test-utils';
import sendRecordsPage from '@/views/pages/subscriptionMsgManagement/tabs/newsPage/sendRecords.vue';
import { Table, Modal } from 'ant-design-vue';
import container from '@/components/modules/container.vue';
import searchForm from '@/components/modules/searchForm/index.vue';
import pagination from '@/components/modules/pagination.vue';
import { useRoute } from 'vue-router';
jest.mock('@/utils/function.js', () => {
  return {
    downloadFile: jest.fn()
  };
});
jest.mock('@/components/modules/searchForm/index.vue', () => ({
  template: '<div class="search-Template">this is searchForm</div>'
}));
jest.mock('@/components/modules/pagination.vue', () => ({
  template: '<div class="pagination-Template">this is pagination</div>'
}));
jest.mock('@/api/modules/subscriptionMsgManagement.js', () => {
  return {
    miniappTemplateTasksContinue: jest.fn(() => Promise.resolve({ data: { 1: 100, 2: 40 } })),
    miniappTemplateTasksList: jest.fn(() =>
      Promise.resolve({
        data: {
          list: [
            { status: 2, taskId: 1 },
            { status: 0, taskId: 2 },
            { status: 5, taskId: 3 },
            { status: 4, taskId: 4 },
            { status: 10, taskId: 10 },
            { status: 2, taskId: 5 }
          ],
          totalCount: 6
        }
      })
    ),
    miniappTemplateTasksdownloadError: jest.fn(() => 'publicPath')
  };
});
useRoute.mockReturnValue({
  query: {
    id: '1'
  }
});
describe('test sendRecordsPage index page', () => {
  let wrapper;
  beforeEach(() => {
    wrapper = mount(sendRecordsPage, {
      global: {
        components: {
          container,
          'a-modal': Modal,
          'a-table': Table,
          searchForm,
          pagination
        },
        stubs: ['a-progress', 'navHeader']
      }
    });
  });
  afterEach(() => {
    jest.clearAllMocks();
  });
  it('test table operation bar', async () => {
    const linkToNodeRecord = wrapper.findAll('.general-link-to');
    expect(linkToNodeRecord.length).toBeGreaterThan(0);
    linkToNodeRecord.forEach(async el => {
      await el.trigger('click');
    });
  });
  it('test template modal', async () => {
    const cusModalNode = wrapper.findComponent(Modal);
    expect(cusModalNode.exists()).toBeTruthy();
    cusModalNode.vm.$emit('update:open', true);
  });
});
