import { mount } from '@vue/test-utils';
import customTemplatePage from '@/views/pages/subscriptionMsgManagement/tabs/newsPage/index.vue';
import { Table,  Button ,Image} from 'ant-design-vue';
import searchForm from '@/components/modules/searchForm/index.vue';
import pagination from '@/components/modules/pagination.vue';
import sendSubscriptionMsg from '@/views/pages/subscriptionMsgManagement/dialog/sendSubscriptionMsg.vue';
import subscriptionMsgPreview from '@/views/pages/subscriptionMsgManagement/dialog/subscriptionMsgPreview.vue';
jest.mock('@/components/modules/searchForm/index.vue', () => ({
  template: '<div class="search-Template">this is searchForm</div>'
}));
jest.mock('@/components/modules/pagination.vue', () => ({
  template: '<div class="pagination-Template">this is pagination</div>'
}));
jest.mock('@/views/pages/subscriptionMsgManagement/dialog/subscriptionMsgPreview.vue', () => ({
  template: '<div class="subscriptionMsgPreview-Template">this is subscriptionMsgPreview</div>'
}));
jest.mock('@/views/pages/subscriptionMsgManagement/dialog/sendSubscriptionMsg.vue', () => ({
  template: '<div class="sendSubscriptionMsg-Template">this is sendSubscriptionMsg</div>'
}));
jest.mock('@/views/pages/subscriptionMsgManagement/tabs/newsPage/useNewPage.utils.js', () => {
  return {
    useNewPageFn: () => {
      return {
        filterState: {
          filterParams: {},
          filterCriteriaList: []
        },
        tableState: {
          newsList: [
            { id: 1, templates: { title: '模板1' } },
            { id: 2, templates: null }
          ],
          columns: [
            {
              title: '模板类型',
              dataIndex: 'templates.title',
              key: 'templates.title'
            },
            {
              title: '操作',
              dataIndex: 'operation',
              key: 'operation'
            }
          ],
          pageInfo: {
            pageSize: 10,
            count: 0,
            pageNumber: 1
          },
          sendSubscriptionMsgVisible: true,
          curPlanId: '',
          openIds: '',
          subscriptionMsgPreview: true,
          templateDetailInfo: null,
          timplateDetailVisible: true
        },
        searchEvent: jest.fn(),
        getPage: jest.fn(),
        updateMiniTemplateForm: jest.fn(),
        checkDetail: jest.fn(),
        subscriptionMsgPreviewClose: jest.fn(),
        miniSubscriptionMsgPreviewSubmit: jest.fn(),
        delateMiniTemplate: jest.fn(),
        lookSendRecords: jest.fn(),
        miniCustomTemplatesPreview: jest.fn(),
        getNewPageList: jest.fn()
      };
    }
  };
});
describe('test customTemplate index  page', () => {
  let wrapper;
  beforeEach(() => {
    wrapper = mount(customTemplatePage, {
      global: {
        components: {
          'a-table': Table,
          'a-button': Button,
          'a-image': Image,
          searchForm,
          subscriptionMsgPreview,
          pagination,
          sendSubscriptionMsg
        },
      }
    });
  });
  afterEach(() => {
    jest.clearAllMocks();
  });
  it('test searchEvent  Func ', async () => {
    const cusSelectNode = wrapper.findComponent(searchForm);
    expect(cusSelectNode.exists()).toBeTruthy();
    cusSelectNode.vm.$emit('searchEvent', { searchTimeStart: '2023-09-09', searchTimeEnd: '2023-10-10' });
  });
  it('test taskInterruption type cancel Func ', async () => {
    global.window.WXAUTH = {
      jumpTo: jest.fn()
    };
    const cuslinkToNode = wrapper.findAll('.general-link-to');
    expect(cuslinkToNode.length).toBeGreaterThan(0);
    cuslinkToNode.forEach(async (el, index) => {
      await el.trigger('click');
    });
  });
  it('test getPage  Func ', async () => {
    const paginationNode = wrapper.findComponent('.pagination-Template');
    expect(paginationNode.exists()).toBeTruthy();
    paginationNode.vm.$emit('currentChange', 1, 10);
    paginationNode.vm.$emit('currentChange', 1);
  });
  it('test sendSubscriptionMsg  Func ', async () => {
    const linkToNodes = wrapper.findAll('.general-link-to');
    expect(linkToNodes.length).toBeGreaterThan(0);
    await linkToNodes[3].trigger('click');
    await wrapper.vm.$nextTick();
    const sendTmpNode = wrapper.findComponent(sendSubscriptionMsg);
    expect(sendTmpNode.exists()).toBeTruthy();
    sendTmpNode.vm.$emit('update:sendSubscriptionMsgVisible', true);
  });
});
