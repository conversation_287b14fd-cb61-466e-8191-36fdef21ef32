import { mount } from '@vue/test-utils';
import miniUserAssignment from '@/views/pages/subscriptionMsgManagement/userAssignment.vue';
import userAssignment from '@/views/pages/commonModules/userAssignment/index.vue';
jest.mock('@/views/pages/commonModules/userAssignment/index.vue', () => ({
  template: '<div class="userAssignment-Template">this is userAssignment</div>'
}));
describe('test miniUserAssignment  page', () => {
  let wrapper;
  beforeEach(() => {
    wrapper = mount(miniUserAssignment, {
      global: {
        components: {
            userAssignment
        }
      }
    });
  });

  it('test miniUserAssignment page  dom', () => {
    expect(wrapper.html()).toContain('userAssignment-Template');
  });
});
