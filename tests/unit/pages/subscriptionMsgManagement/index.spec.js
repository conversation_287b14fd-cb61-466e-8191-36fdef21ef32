import { mount } from '@vue/test-utils';
import indexPage from '@/views/pages/subscriptionMsgManagement/index.vue';
import { Tabs } from 'ant-design-vue';
import { useRoute } from 'vue-router';
import container from '@/components/modules/container.vue';
import newsPage from '@/views/pages/subscriptionMsgManagement/tabs/newsPage/index.vue';
import templatePage from '@/views/pages/subscriptionMsgManagement/tabs/templatePage/index.vue';
jest.mock('@/views/pages/subscriptionMsgManagement/tabs/newsPage/index.vue', () => ({
  template: '<div class="news-Template">this is newsPage</div>'
}));
jest.mock('@/views/pages/subscriptionMsgManagement/tabs/templatePage/index.vue', () => ({
  template: '<div class="template-Template">this is templatePage</div>'
}));
describe('test subscriptionMsgManagement index page', () => {
  let wrapper;
  beforeEach(() => {
    wrapper = mount(indexPage, {
      global: {
        components: {
          container,
          'a-tabs': Tabs,
          'a-tab-pane': Tabs.TabPane,
          newsPage,
          templatePage
        },
        stubs:['navHeader']
      }
    });
  });
  it('test tag highlight attribute', async () => {
    useRoute.mockReturnValueOnce({
      query: {
        activeName: 'template'
      }
    });
    const tabsNode = wrapper.findComponent(Tabs);
    expect(tabsNode.exists()).toBe(true);
    await tabsNode.vm.$emit('update:activeKey', 'watchTemplate');
    await tabsNode.vm.$nextTick();
    expect(tabsNode.vm.activeKey).toBe('watchTemplate');
  });
  it('test tag highlight attribute equals template', async () => {
    useRoute.mockReturnValueOnce({
      query: {
        activeName: 'news'
      }
    });
    expect(wrapper.html()).toContain('class="template-Template"');
  });
  it('test highlight attribute equals subscription message', () => {
    expect(wrapper.html()).toContain('class="news-Template"');
  });
});
