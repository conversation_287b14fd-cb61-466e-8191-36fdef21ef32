import { mount } from '@vue/test-utils';
import watchTemplatePage from '@/views/pages/tplMsgManagement/tabs/watchTemplate/index.vue';
import { Button, Table } from 'ant-design-vue';
import container from '@/components/modules/container.vue';
import updateLanguageEn from '@/views/pages/commonModules/modal/updateLanguageEn.vue';
import templateDetail from '@/views/pages/tplMsgManagement/dialog/templateDetail.vue';
jest.mock('@/components/modules/searchForm/index.vue', () => ({
  template: '<div class="search-Template">this is searchForm</div>'
}));
jest.mock('@/components/modules/pagination.vue', () => ({
  template: '<div class="pagination-Template">this is pagination</div>'
}));
jest.mock('@/views/pages/commonModules/modal/updateLanguageEn.vue', () => ({
  template: '<div class="language-Template">this is updateLanguageEn</div>'
}));
jest.mock('@/views/pages/tplMsgManagement/dialog/templateDetail.vue', () => ({
  template: '<div class="detail-Template">this is templateDetail</div>'
}));
jest.mock('@/views/pages/commonModules/modal/useUpdateLanguageEn.utils.js', () => {
  return {
    useUpdateLanguageEnFn: jest
      .fn(() => {
        return {
          updateLanguageState: {
            updateLanguageVisible: true,
            updateLanguageId: 1,
            updateLanguageFormFieldList: []
          },
          updateTemplateLanguage: jest.fn()
        };
      })
      .mockReturnValueOnce({
        updateLanguageState: {
          updateLanguageVisible: false,
          updateLanguageId: 1,
          updateLanguageFormFieldList: []
        },
        updateTemplateLanguage: jest.fn()
      })
  };
});
jest.mock('@/api/modules/tplMsgManagement.js', () => {
  return {
    checkTemplate: jest.fn(),
    templatesList: jest.fn(() =>
      Promise.resolve({
        data: {
          list: [
            {
              id: 1,
              title: '标题1',
              templateId: 11,
              status: false,
              modifiedDate: '2024-09-09',
              contentEn: 'start{{first.DATA}}\nlevel{{keyword1.DATA}}\ninfo{{keyword2.DATA}}\nremark{{remark.DATA}}',
              content: '{{first.DATA}}\n错误级别：{{keyword1.DATA}}\n错误信息：{{keyword2.DATA}}\n{{remark.DATA}}'
            },
            {
              id: 2,
              title: '标题2',
              templateId: 22,
              status: true,
              modifiedDate: '2024-09-09',
              contentEn: '',
              content: '{{first.DATA}}\n验证码：{{keyword1.DATA}}\n时间：{{keyword2.DATA}}\n{{remark.DATA}}'
            }
          ],
          totalCount: 2
        }
      })
    )
  };
});
describe('test watchTemplatePage index page', () => {
  let wrapper;
  beforeEach(() => {
    wrapper = mount(watchTemplatePage, {
      global: {
        components: {
          container,
          'a-button': Button,
          'a-table': Table,
          templateDetail
        },
        stubs: {
          updateLanguageEn,
          searchForm: true,
          pagination: true
        }
      }
    });
  });
  it('test page linkto Func', async () => {
    const linkToNodeWatch = wrapper.findAll('.general-link-to');
    expect(linkToNodeWatch.length).toBeGreaterThan(0);
    linkToNodeWatch.forEach(async (el, index) => {
      await el.trigger('click');
    });
  });
  it('test updateLanguageEn component Func', async () => {
    const updateLanguageEnNode = wrapper.findComponent(updateLanguageEn);
    expect(updateLanguageEnNode.exists()).toBeTruthy();
    updateLanguageEnNode.vm.$emit('update:updateLanguageVisible', false);
  });
  it('test templateDetail component Func', async () => {
    const linkToNodeTemp = wrapper.findAll('.general-link-to');
    expect(linkToNodeTemp.length).toBeGreaterThan(0);
    await linkToNodeTemp[0].trigger('click');
    await wrapper.vm.$nextTick();
    const templateDetailNode = wrapper.findComponent(templateDetail);
    expect(templateDetailNode.exists()).toBeTruthy();
    templateDetailNode.vm.$emit('update:templateDetailVisible', false);
  });
});
