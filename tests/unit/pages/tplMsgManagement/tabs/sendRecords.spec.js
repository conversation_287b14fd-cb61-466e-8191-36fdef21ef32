import { mount } from '@vue/test-utils';
import sendRecordsPage from '@/views/pages/tplMsgManagement/tabs/sendRecords/index.vue';
import { Table, Modal } from 'ant-design-vue';
import container from '@/components/modules/container.vue';
import searchForm from '@/components/modules/searchForm/index.vue';
import pagination from '@/components/modules/pagination.vue';
import apis from '@/api/modules/tplMsgManagement.js';
jest.mock('@/components/modules/searchForm/index.vue', () => ({
  template: '<div class="search-Template">this is searchForm</div>'
}));
jest.mock('@/components/modules/pagination.vue', () => ({
  template: '<div class="pagination-Template">this is pagination</div>'
}));
jest.mock('@/utils/function.js',()=>{
  return {
    downloadFile:jest.fn()  
  }
})
jest.mock('@/api/modules/tplMsgManagement.js', () => {
  return {
    customTemplateMsgAction: jest.fn(() => Promise.resolve({ data: { 1: 100, 2: 40 } })),
    templateTasksList: jest.fn(() =>
      Promise.resolve({
        data: {
          list: [
            {
              taskId: '1811232187673088000',
              title: '审核结果通知',
              name: null,
              status: 1,
              total: null,
              sendSuccess: null,
              sendFail: null,
              sendTime: null,
              createdAt: '2024-07-11 02:52:59',
              createBy: null
            },
            {
              taskId: '333333',
              title: '审核结果通知',
              name: null,
              status: 2,
              total: null,
              sendSuccess: null,
              sendFail: null,
              sendTime: null,
              createdAt: '2024-07-11 02:52:59',
              createBy: null
            },
            {
              taskId: '1811232384016846848',
              title: '审核结果通知',
              name: null,
              status: 3,
              total: null,
              sendSuccess: null,
              sendFail: null,
              sendTime: null,
              createdAt: '2024-07-11 02:53:46',
              createBy: null
            },
            {
              taskId: '5555',
              title: '审核结果通知',
              name: null,
              status: 0,
              total: null,
              sendSuccess: null,
              sendFail: null,
              sendTime: null,
              createdAt: '2024-07-11 02:52:59',
              createBy: null
            }
          ],
          totalCount: 4
        }
      })
    ),
    downloadError: jest.fn(() => 'publicPath')
  };
});
describe('test sendRecordsPage index page', () => {
  let wrapper;
  beforeEach(() => {
    wrapper = mount(sendRecordsPage, {
      global: {
        components: {
          container,
          'a-modal': Modal,
          'a-table': Table,
          searchForm,
          pagination
        },
        stubs: ['a-progress']
      }
    });
  });
  afterEach(() => {
    jest.clearAllMocks();
  });
  it('test taskInterruption type cancel Func ', async () => {
    jest.spyOn(Modal, 'confirm').mockImplementation(params => {
      if (params.onCancel) {
        params.onCancel();
      }
      if (params.onOk) {
        params.onOk();
      }
    });
    jest.spyOn(window, 'setInterval').mockImplementation(fn => fn());
    const linkToNodeRecord = wrapper.findAll('.general-link-to');
    expect(linkToNodeRecord.length).toBeGreaterThan(0);
    linkToNodeRecord.forEach(async el => {
      await el.trigger('click');
    });
    wrapper.vm.tableState.listenSendStatusIds = { 1: 0 };
    wrapper.vm.taskInterruption({ taskId: 1 }, 'continue');
    Modal.confirm.mockRestore();
  });
  it('test modal   Func ', async () => {
    const modalNodeRecord = wrapper.findComponent(Modal);
    expect(modalNodeRecord.exists()).toBeTruthy();
    modalNodeRecord.vm.$emit('update:open', true);
  });
  it('test searchEvent  Func ', async () => {
    const selectNodeRecord = wrapper.findComponent(searchForm);
    expect(selectNodeRecord.exists()).toBeTruthy();
    selectNodeRecord.vm.$emit('searchEvent', { searchTimeStart: '2023-09-09', searchTimeEnd: '2023-10-10' });
  });
  it('test getPage  Func ', async () => {
    const paginationNodes = wrapper.findComponent('.pagination-Template');
    expect(paginationNodes.exists()).toBeTruthy();
    paginationNodes.vm.$emit('currentChange', 1, 10);
    wrapper.vm.getPage(1);
  });
  it('test downdetailed  Func ', async () => {
    wrapper.vm.downdetailed(4);
  });
  it('test listenSendStatus Func', async () => {
    const mockClearInterval = jest.spyOn(window, 'clearInterval');
    wrapper.vm.tableState.listenSendStatusIds = { 1: 0, 2: 0 };
    wrapper.vm.sendreportPreviewClose();
    wrapper.vm.tableState.listenSendStatusIds = {};
    await wrapper.vm.listenSendStatus();
    await wrapper.vm.listenSendStatus();
    jest.spyOn(apis, 'customTemplateMsgAction').mockResolvedValueOnce({ data: { 1: 100 } });
    wrapper.vm.tableState.listenSendStatusIds = { 1: 0 };
    wrapper.vm.tableState.sendRecordList = [{taskId:1,status:1}];
    await wrapper.vm.listenSendStatus();
    expect(mockClearInterval).toHaveBeenCalled();
  });
});
