import { mount } from '@vue/test-utils';
import newTemplateFormPage from '@/views/pages/tplMsgManagement/tabs/customTemplate/newTemplateForm.vue';
import container from '@/components/modules/container.vue';
import { Select, Form, Input, Button } from 'ant-design-vue';
jest.mock('@/api/modules/tplMsgManagement.js', () => {
  return {
    templatesList: jest.fn(() =>
      Promise.resolve({
        data: {
          list: [
            {
              id: 1,
              appid: 'wx95de8515af35942d',
              status: 1,
              templateId: 'z_v1aMtlEyGmOSdrQPCQcJZjj_cR4Cp-5FpnSNGADXQ',
              title: '服务器报警通知',
              primaryIndustry: 'IT科技',
              deputyIndustry: '互联网|电子商务',
              content: '{{first.DATA}}\n错误级别：{{keyword1.DATA}}\n错误信息：{{keyword2.DATA}}\n{{remark.DATA}}',
              contentEn: 'start{{first.DATA}}\nlevel{{keyword1.DATA}}\ninfo{{keyword2.DATA}}\nremark{{remark.DATA}}',
              example: '服务器出错啦\n错误级别：警告\n\n错误信息：参数未定义请尽快处理\n',
              createdAt: '2024-07-10 03:59:54',
              updatedAt: '2024-07-12 01:23:27'
            }
          ],
          totalCount: 1
        }
      })
    ),
    createTemplatePlans: jest.fn(),
    detailTemplatePlans: jest.fn(() =>
      Promise.resolve({
        data: {
          wechatTemplate: {
            id: '1810886640860794880',
            appid: 'wx95de8515af35942d',
            status: 1,
            templateId: 'z_v1aMtlEyGmOSdrQPCQcJZjj_cR4Cp-5FpnSNGADXQ',
            title: '服务器报警通知',
            primaryIndustry: 'IT科技',
            deputyIndustry: '互联网|电子商务',
            content: '{{first.DATA}}\n错误级别：{{keyword1.DATA}}\n错误信息：{{keyword2.DATA}}\n{{remark.DATA}}',
            contentEn: 'start{{first.DATA}}\nlevel{{keyword1.DATA}}\ninfo{{keyword2.DATA}}\nremark{{remark.DATA}}',
            example: '服务器出错啦\n错误级别：警告\n\n错误信息：参数未定义请尽快处理\n',
            createdAt: '2024-07-10 03:59:54',
            updatedAt: '2024-07-12 01:23:27'
          },
          customTemplate: {
            id: '1813484901811490816',
            appid: 'wx95de8515af35942d',
            templateId: 1,
            name: '服务器报警',
            url: null,
            content: '{"url":"publicPath","miniprogram":{"appid":"sssss","pagepath":"fdfdfdf"},"data":{}}',
            createdAt: '2024-07-17 16:04:28',
            updatedAt: '2024-07-18 05:37:54'
          }
        }
      })
    ),
    templatePlansEdit: jest.fn()
  };
});
jest.mock('@/utils/function', () => ({
  copyProperty: jest.fn((source, data) => data)
}));
describe('test newTemplateForm page', () => {
  let wrapper;
  beforeEach(() => {
    wrapper = mount(newTemplateFormPage, {
      global: {
        components: {
          container,
          'a-form': Form,
          'a-form-item': Form.Item,
          'a-select': Select,
          'a-input': Input,
          'a-select-option': Select.Option,
          'a-button': Button
        },
        stubs:['navHeader'],
        directives: {
          debounce: jest.fn(v => v)
        }
      }
    });
  });
  afterEach(() => {
    jest.clearAllMocks();
  });
  it('test form select Func and Branch ', async () => {
    const newSelectNode = wrapper.findComponent(Select);
    expect(newSelectNode.exists()).toBeTruthy();
    await newSelectNode.vm.$emit('update:value', 1);
    await newSelectNode.vm.$nextTick();
    expect(newSelectNode.vm.value).toBe(1);
  });
  it('test form inputs Func and Branch ', async () => {
    const newInputNodeArr = wrapper.findAllComponents(Input);
    expect(newInputNodeArr.length).toBeGreaterThan(0);
    newInputNodeArr.forEach(async (el, index) => {
      await el.vm.$emit('update:value', index);
      await el.vm.$nextTick();
      if (index < 5) {
        expect(el.vm.value).toEqual(index);
      }
    });
  });
});
