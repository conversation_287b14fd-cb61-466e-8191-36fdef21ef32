import { mount } from '@vue/test-utils';
import historicalDataPage from '@/views/pages/tplMsgManagement/tabs/customTemplate/historicalData.vue';
import container from '@/components/modules/container.vue';
import pagination from '@/components/modules/pagination.vue';
import { useRoute } from 'vue-router';
import { Table } from 'ant-design-vue';
jest.mock('@/components/modules/pagination.vue', () => ({
  template: '<div class="pagination-Template">this is pagination</div>'
}));
jest.mock('@/api/modules/tplMsgManagement.js', () => {
  return {
    getPlanHistory: jest.fn(() =>
      Promise.resolve({
        data: {
          list: [
            {
              createdDate: '2024-09-01',
              sendAt:'2024-09-09',
              sendSuccess: 2,
              total: 1,
              sendFail: 2,
              sendTime: 4
            }
          ],
          totalCount: 1
        }
      })
    )
  };
});
useRoute.mockReturnValueOnce({
  query: { id: 1, title: 'test Template', name: 'template notification', templateId: 11 }
});
describe('test historicalDataPage index page', () => {
  let wrapper;
  beforeEach(() => {
    wrapper = mount(historicalDataPage, {
      global: {
        components: {
          container,
          'a-table': Table,
          pagination
        },
        stubs:['navHeader']
      }
    });
  });
  afterEach(() => {
    jest.clearAllMocks();
  });
  it('test page Func and Branch ', async () => {
    expect(wrapper.html()).toContain('this is pagination');
  });
});
