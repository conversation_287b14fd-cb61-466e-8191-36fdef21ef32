import { mount } from '@vue/test-utils';
import customTemplatePage from '@/views/pages/tplMsgManagement/tabs/customTemplate/index.vue';
import { Table, Button, } from 'ant-design-vue';
import container from '@/components/modules/container.vue';
import searchForm from '@/components/modules/searchForm/index.vue';
import pagination from '@/components/modules/pagination.vue';
import sendTmplateMsg from '@/views/pages/tplMsgManagement/dialog/sendTmplateMsg.vue';
import msgPreviewTemp from '@/views/pages/tplMsgManagement/dialog/msgPreviewTemp.vue';
jest.mock('@/components/modules/searchForm/index.vue', () => ({
  template: '<div class="search-Template">this is searchForm</div>'
}));
jest.mock('@/views/pages/tplMsgManagement/dialog/msgPreviewTemp.vue', () => ({
  template: '<div class="template-preview">this is preview temp</div>'
}));
jest.mock('@/components/modules/pagination.vue', () => ({
  template: '<div class="pagination-Template">this is pagination</div>'
}));
jest.mock('@/views/pages/tplMsgManagement/dialog/sendTmplateMsg.vue', () => ({
  template: '<div class="sendTmplateMsg-Template">this is sendTmplateMsg</div>'
}));
jest.mock('@/api/modules/tplMsgManagement.js', () => {
  return {
    createTemplatePlans: jest.fn(),
    templatePlansDel: jest.fn(),
    customTemplates: jest
      .fn(() =>
        Promise.resolve({
          data: {
            totalCount: '1',
            list: [
              {
                id: '3xx',
                title: '服务器报警通知',
                appid: '4xxxx',
                templateId: '12xxx',
                name: '服务器报警',
                url: null,
                content: '{"url":"publicPath","miniprogram":{"appid":"sssss","pagepath":"fdfdfdf"},"data":{}}',
                createdAt: '2024-07-17 16:04:28',
                updatedAt: '2024-07-18 05:37:54'
              }
            ]
          }
        })
      )
      .mockResolvedValueOnce({ data: { list: null, totalCount: 0 } })
  };
});
describe('test customTemplate index  page', () => {
  let wrapper;
  beforeEach(() => {
    wrapper = mount(customTemplatePage, {
      global: {
        components: {
          container,
          'a-table': Table,
          'a-button': Button,
          searchForm,
          pagination,
          sendTmplateMsg,
          msgPreviewTemp
        }
      }
    });
  });
  afterEach(() => {
    jest.clearAllMocks();
  });
  it('test searchEvent  Func ', async () => {
    const cusSelectNode = wrapper.findComponent(searchForm);
    expect(cusSelectNode.exists()).toBeTruthy();
    cusSelectNode.vm.$emit('searchEvent', { searchTimeStart: '2023-09-09', searchTimeEnd: '2023-10-10' });
  });
  it('test taskInterruption type cancel Func ', async () => {
    global.window.WXAUTH = {
      jumpTo: jest.fn()
    };
    const cuslinkToNode = wrapper.findAll('.general-link-to');
    expect(cuslinkToNode.length).toBeGreaterThan(0);
    cuslinkToNode.forEach(async (el, index) => {
      await el.trigger('click');
    });
  });
  it('test getPage  Func ', async () => {
    const paginationNode = wrapper.findComponent('.pagination-Template');
    expect(paginationNode.exists()).toBeTruthy();
    paginationNode.vm.$emit('currentChange', 1, 10);
    paginationNode.vm.$emit('currentChange', 1);
  });
  it('test sendTmplateMsg  Func ', async () => {
    const linkToNodes = wrapper.findAll('.general-link-to');
    expect(linkToNodes.length).toBeGreaterThan(0);
    await linkToNodes[2].trigger('click');
    await wrapper.vm.$nextTick();
    const sendTmpNode = wrapper.findComponent(sendTmplateMsg);
    expect(sendTmpNode.exists()).toBeTruthy();
    sendTmpNode.vm.$emit('update:sendTmplateMsgVisible', true);
  });
});
