import apis from '@/api/modules/tplMsgManagement.js';
import { Modal } from 'ant-design-vue';
jest.mock('@/api/modules/tplMsgManagement.js', () => {
  return {
    createTemplatePlans: jest.fn(),
    templatePlansDel: jest.fn(),
    customTemplates: jest
      .fn(() =>
        Promise.resolve({
          data: {
            totalCount: '1',
            list: [
              {
                id: '2344444',
                title: '活动促销通知',
                appid: 'wxxxxxx',
                templateId: '222xxx',
                name: '活动促销',
                url: null,
                content: '{"url":"publicPath","miniprogram":{"appid":"sssss","pagepath":"fdfdfdf"},"data":{}}',
                createdAt: '2024-07-17 16:04:28',
                updatedAt: '2024-07-18 05:37:54'
              }
            ]
          }
        })
      )
      .mockResolvedValueOnce({ data: { list: null, totalCount: 0 } })
  };
});
global.window.WXAUTH = {
  jumpTo: jest.fn()
};
jest.spyOn(Modal, 'confirm').mockImplementation(params => {
    if (params.onCancel) {
      params.onCancel();
    }
    if (params.onOk) {
      params.onOk();
    }
  });
const { useCustomTemplateFn } = require('@/views/pages/tplMsgManagement/tabs/customTemplate/useCustomTemplate.utils.js');
describe('test the useCustomsTemplate.utls.js file', () => {
  afterEach(() => {
    jest.clearAllMocks();
  });
  it('test the getTemplatePlansList method', async () => {
    const { tableState, getTemplatePlansList, filterState } = useCustomTemplateFn();
    await getTemplatePlansList();
    filterState.filterParams.searchTimeStart = '2023-09-09';
    filterState.filterParams.searchTimeEnd = '2023-10-19';
    await getTemplatePlansList(true);
    expect(tableState.newsList.length).toBe(1);
  });
  it('test the searchEvent method ', async () => {
    const { searchEvent, tableState } = useCustomTemplateFn();
    const queryPar = { name: 'ling' };
    await searchEvent(queryPar);
    expect(tableState.newsList.length).toBe(1);
  });
  it('test the getPage method ', async () => {
    const { getPage, tableState } = useCustomTemplateFn();
    await getPage(1);
    await getPage(1, 10);
    expect(tableState.pageInfo.pageNumber).toBe(1);
  });
  it('test the updateTemplateForm  method', () => {
    const { updateTemplateForm } = useCustomTemplateFn();
    updateTemplateForm({ id: '' });
    updateTemplateForm({ id: 1 });
  });
  it('test the delateTemplate method', async () => {
    const { delateTemplate } = useCustomTemplateFn();
    await delateTemplate(1);
    expect(apis.templatePlansDel).toHaveBeenCalled()

  });
  it('test the lookHistoricalData method', async () => {
    const { lookHistoricalData } = useCustomTemplateFn();
    await lookHistoricalData({ id: 1, title: 'title', name: 'name', templateId: 1 });
    expect(window.WXAUTH.jumpTo).toHaveBeenCalled();
  });
});
