import { mount } from '@vue/test-utils';
import tplUserAssignment from '@/views/pages/tplMsgManagement/userAssignment.vue';
import userAssignment from '@/views/pages/commonModules/userAssignment/index.vue';
jest.mock('@/views/pages/commonModules/userAssignment/index.vue', () => ({
  template: '<div class="userAssignment-Template">this is userAssignment</div>'
}));
describe('test tplUserAssignment  page', () => {
  let wrapper;
  beforeEach(() => {
    wrapper = mount(tplUserAssignment, {
      global: {
        components: {
            userAssignment
        }
      }
    });
  });

  it('test tplUserAssignment page  dom', () => {
    expect(wrapper.html()).toContain('userAssignment-Template');
  });
});
