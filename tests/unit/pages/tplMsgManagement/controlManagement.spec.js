import { mount } from '@vue/test-utils';
import tplControlManagement from '@/views/pages/tplMsgManagement/controlManagement.vue';
import controlManagement from '@/views/pages/commonModules/controlManagement/index.vue';
jest.mock('@/views/pages/commonModules/controlManagement/index.vue', () => ({
  template: '<div class="controlManagement-Template">this is controlManagement</div>'
}));
describe('test tplControlManagement  page', () => {
  let wrapper;
  beforeEach(() => {
    wrapper = mount(tplControlManagement, {
      global: {
        components: {
          controlManagement
        }
      }
    });
  });

  it('test tplControlManagement page  dom', () => {
    expect(wrapper.html()).toContain('controlManagement-Template');
  });
});
