import { mount } from '@vue/test-utils';
import sendTmplateMsgPage from '@/views/pages/tplMsgManagement/dialog/sendTmplateMsg.vue';
import { Modal, Select, Form, Button, Upload, DatePicker } from 'ant-design-vue';
jest.mock('@/api/modules/tplMsgManagement.js', () => {
  return {
    customTemplateMsgAction: jest.fn(),
    downloadTemplate: jest.fn(() => 'publicPath')
  };
});
describe('test sendTmplateMsgPage index page', () => {
  let wrapper;
  beforeEach(() => {
    wrapper = mount(sendTmplateMsgPage, {
      global: {
        components: {
          'a-modal': Modal,
          'a-form': Form,
          'a-form-item': Form.Item,
          'a-select': Select,
          'a-select-option': Select.Option,
          'a-upload': Upload,
          'a-button': Button,
          'a-date-picker': DatePicker
        }
      },
      props: {
        sendTmplateMsgVisible: true,
        curPlanId: 1
      }
    });
  });
  afterEach(() => {
    jest.clearAllMocks();
  });
  it('test send modal open  Func ', async () => {
    const sendModalNode = wrapper.findComponent(Modal);
    expect(sendModalNode.exists()).toBeTruthy();
    await sendModalNode.vm.$emit('update:open', true);
    expect(sendModalNode.vm.open).toBe(true);
  });
  it('test form  Func ', async () => {
    const sendSelectNode = wrapper.findComponent(Select);
    expect(sendSelectNode.exists()).toBeTruthy();
    await sendSelectNode.vm.$emit('update:value', 1);
    await sendSelectNode.vm.$nextTick();
    const dateNode = wrapper.findComponent(DatePicker);
    expect(dateNode.exists()).toBeTruthy();
    await dateNode.vm.$emit('update:value', '2021-09-09 10:10:00');
    expect(dateNode.vm.value).toBe('2021-09-09 10:10:00');
  });
  it('test upload Func', () => {
    const uploadNode = wrapper.findComponent(Upload);
    expect(uploadNode.exists()).toBeTruthy();
    uploadNode.vm.$emit('beforeUpload');
  });
});
