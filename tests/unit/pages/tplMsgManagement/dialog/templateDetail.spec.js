jest.unmock('ant-design-vue');
import { mount } from '@vue/test-utils';
import templateDetailPage from '@/views/pages/tplMsgManagement/dialog/templateDetail.vue';
import { Modal } from 'ant-design-vue';
describe('test templateDetailPage index page', () => {
  let wrapper;
  beforeEach(() => {
    wrapper = mount(templateDetailPage, {
      global: {
        components: {
          'a-modal': Modal
        }
      },
      props: {
        templateDetailVisible: true,
        templateDetailInfo: {
          content: { a: 1, b: 2 }
        }
      }
    });
  });
  afterEach(() => {
    jest.clearAllMocks();
  });
  it('test template modal open  Func ', async () => {
    await wrapper.vm.$nextTick();
    const tempModalNode = wrapper.findComponent(Modal);
    expect(tempModalNode.exists()).toBeTruthy();
    await tempModalNode.vm.$emit('update:open', true);
    expect(tempModalNode.vm.open).toBe(true);
    await tempModalNode.vm.$nextTick();
    wrapper.setProps({
      templateDetailVisible: false,
      templateDetailInfo: null
    });
  });
});
