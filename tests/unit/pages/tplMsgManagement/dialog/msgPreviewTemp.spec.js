import { mount } from '@vue/test-utils';
import msgPreviewTemp from '@/views/pages/tplMsgManagement/dialog/msgPreviewTemp.vue';
import { Modal, Button } from 'ant-design-vue';
jest.mock('@/api/modules/tplMsgManagement.js', () => {
  return {
    createTemplatePlans: jest.fn()
  };
});
describe('test msgPreviewTemp index page', () => {
  let wrapper;
  beforeEach(() => {
    wrapper = mount(msgPreviewTemp, {
        global: {
          components: {
            'a-modal': Modal,
            'a-button': Button,
          },
          stubs:['a-textarea']
        },
        props: {
          subscriptionMsgPreview: true,
          curPlanId: 1
        }
      });
  });
  afterEach(() => {
    jest.clearAllMocks();
  });
  it('test sending preview DOM', async () => {
    await wrapper.vm.$nextTick();
    const msgModalNode = wrapper.findComponent(Modal);
    expect(msgModalNode.exists()).toBeTruthy();
    await msgModalNode.vm.$emit('update:open', true);
    expect(msgModalNode.vm.open).toBe(true);
  });
});
