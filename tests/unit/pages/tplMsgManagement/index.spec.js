import { mount } from '@vue/test-utils';
import indexPage from '@/views/pages/tplMsgManagement/index.vue';
import { Tabs } from 'ant-design-vue';
import { useRoute } from 'vue-router';
import container from '@/components/modules/container.vue';
import watchTemplate from '@/views/pages/tplMsgManagement/tabs/watchTemplate/index.vue';
import customTemplate from '@/views/pages/tplMsgManagement/tabs/customTemplate/index.vue';
import sendRecords from '@/views/pages/tplMsgManagement/tabs/sendRecords/index.vue';
jest.mock('@/views/pages/tplMsgManagement/tabs/watchTemplate/index.vue', () => ({
  template: '<div class="watch-Template">this is watchTemplate</div>'
}));
jest.mock('@/views/pages/tplMsgManagement/tabs/customTemplate/index.vue', () => ({
  template: '<div class="custom-Template">this is customTemplate</div>'
}));
jest.mock('@/views/pages/tplMsgManagement/tabs/sendRecords/index.vue', () => ({
  template: '<div class="send-Template">this is sendRecords</div>'
}));
describe('test tplMsgManagement index page', () => {
  let wrapper;
  beforeEach(() => {
    wrapper = mount(indexPage, {
      global: {
        components: {
          container,
          'a-tabs': Tabs,
          'a-tab-pane': Tabs.TabPane,
          watchTemplate,
          customTemplate,
          sendRecords
        },
        stubs:['navHeader']
      }
    });
  });
  it('test page Tabs Func ', async () => {
    useRoute.mockReturnValueOnce({
      query: {
        activeName: 'customTemplate'
      }
    });
    const tabsNode = wrapper.findComponent(Tabs);
    expect(tabsNode.exists()).toBe(true);
    await tabsNode.vm.$emit('update:activeKey', 'watchTemplate');
    await tabsNode.vm.$nextTick();
    expect(tabsNode.vm.activeKey).toBe('watchTemplate');
  });
  it('test page activeName attribute', async () => {
    useRoute.mockReturnValueOnce({
      query: {
        activeName: 'sendRecord'
      }
    });
    expect(wrapper.html()).toContain('class="custom-Template"');
  });
  it('test page activeName attribute two', () => {
    expect(wrapper.html()).toContain('class="send-Template"');
  });
});
