import { mount } from '@vue/test-utils';
import index from '@/views/pages/applicationManagement/index.vue';
import { Table, Button } from 'ant-design-vue';
import container from '@/components/modules/container.vue';
jest.mock('vuex', () => ({
  useStore: jest.fn(() => {
    return {
      state: {
        systemApplicationInfo: {
          applicationList: [
            { id: 1, wechatName: '雅戈尔', appTypeName: '公众号', type: 'service', createdAt: '2021-09-09' },
            { id: 2, wechatName: '潘多拉', appTypeName: '小程序', type: 'miniapp', createdAt: '2021-09-09' }
          ]
        }
      },
      dispatch: jest.fn()
    };
  })
}));
describe('test applicationManagement index page', () => {
  let wrapper;
  beforeEach(() => {
    wrapper = mount(index, {
      global: {
        components: {
          container,
          'a-table': Table,
          'a-button': But<PERSON>
        },
        directives: {
          debounce: jest.fn(v => v)
        }
      }
    });
  });
  it('test table dom', async () => {
    const ghostBtnNode = wrapper.findAll('.ant-btn-primary');
    expect(ghostBtnNode.length).toBeGreaterThan(0);
    ghostBtnNode.forEach(async el => {
      await el.trigger('click');
    });
    const linkToNode = wrapper.findAll('.general-link-to');
    expect(linkToNode.length).toBeGreaterThan(0);
    linkToNode.forEach(async el => {
      await el.trigger('click');
    });
  });
});
