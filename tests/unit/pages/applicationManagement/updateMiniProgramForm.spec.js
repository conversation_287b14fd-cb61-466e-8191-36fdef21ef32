import { mount } from '@vue/test-utils';
import './mockCommon.js';
import { useRoute } from 'vue-router';
import updateMiniProgramForm from '@/views/pages/applicationManagement/updateMiniProgramForm.vue';
import { Select, Form, Input, Button, Radio ,Image} from 'ant-design-vue';
import container from '@/components/modules/container.vue';
jest.mock('@/api/modules/applicationManagement.js', () => ({
  miniWechatSave: jest.fn(() => Promise.resolve({ data: { code: 200 } })),
  miniWechatDetail: jest
    .fn(() =>
      Promise.resolve({
        code: 200,
        data: {
          id: 1,
          status: 1,
          organizationId: 1,
          appid: '1',
          appkey: 'cust',
          accesstokenFetch: '{"url":"urlAdd","structure":"access_token"}',
          token: '23333333333333',
          aeskey: '1',
          encryptCode: '1',
          wechatOriginalId: '5555555555',
          type: 'miniapp',
          joinWay: 'open',
          wxid: 'woaap_etocrm',
          connectUrl: null,
          wechatName: 'QQ音乐公众号',
          logo: 'urlAdd',
          description: 'WOAAP管理平台',
          isWachatAc: true,
          createdDate: '2018-11-15 20:58:58',
          updatedAt: '2024-07-09 09:04:25'
        }
      })
    )
    .mockResolvedValueOnce({
      code: 200,
      data: {
        id: 1,
        status: 1,
        organizationId: 1,
        appid: '2',
        appkey: 'cust',
        accesstokenFetch: '',
        token: '23333333333333',
        aeskey: '2',
        encryptCode: '2',
        wechatOriginalId: '5555555555',
        type: 'miniapp',
        joinWay: 'open',
        wxid: 'woaap_etocrm',
        connectUrl: null,
        wechatName: 'QQ音乐公众号',
        logo: 'urlAdd',
        description: 'WOAAP管理平台',
        isWachatAc: false,
        createdDate: '2018-11-15 20:58:58',
        updatedAt: '2024-07-09 09:04:25'
      }
    })
}));
describe('test pagination components', () => {
  let wrapper;
  beforeEach(() => {
    wrapper = mount(updateMiniProgramForm, {
      global: {
        components: {
          container,
          'a-form': Form,
          'a-form-item': Form.Item,
          'a-select': Select,
          'a-input': Input,
          'a-input-group': Input.Group,
          'a-radio-group': Radio.Group,
          'a-radio-button': Radio.Button,
          'a-select-option': Select.Option,
          'a-button': Button,
          'a-image':Image
        },
        directives: {
          debounce: jest.fn(v => v)
        },
        stubs:['navHeader'],
      }
    });
  });
  afterEach(() => {
    jest.resetModules();
  });
  it('test image selector', async () => {
    const selectNodeOnec = wrapper.findComponent(Select);
    expect(selectNodeOnec.exists()).toBeTruthy();
    await selectNodeOnec.vm.$emit('update:value', 'o2ther');
  });
  it('test form component', async () => {
    const radioGroupNodes = wrapper.findComponent(Radio.Group);
    expect(radioGroupNodes.exists()).toBeTruthy();
    await radioGroupNodes.vm.$emit('update:value', false);
    await radioGroupNodes.vm.$nextTick();
    expect(radioGroupNodes.vm.value).toBe(false);
    await wrapper.vm.$nextTick();
    const inputDomArr = wrapper.findAllComponents(Input);
    expect(inputDomArr.length).toBeGreaterThan(0);
    useRoute.mockReturnValueOnce({
      query: {
        id: 1
      }
    });
    inputDomArr.forEach(async (el, index) => {
      await el.vm.$emit('update:value', index);
      await el.vm.$nextTick();
      expect(el.vm.value).toEqual(index);
    });
  });
});
