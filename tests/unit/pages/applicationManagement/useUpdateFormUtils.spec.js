import './mockCommon.js';
import { useUpdateFormFn } from '@/views/pages/applicationManagement/useUpdateForm.utils.js';
import { message, Modal } from 'ant-design-vue';
import { miniWechatSave } from '@/api/modules/applicationManagement.js';
import { useRoute } from 'vue-router';
jest.mock('@/api/modules/applicationManagement.js', () => ({
  miniWechatSave: jest.fn(() => Promise.resolve({ data: { code: 200 } })),
  wechaApplicationsSave: jest.fn(() => Promise.resolve({ data: { code: 200 } })),
  miniWechatDetail: jest
    .fn(() => Promise.resolve({ data: null }))
    .mockResolvedValueOnce({
      data: { accesstokenFetch: '{"url":"urlAdd","structure":"access_token"}', isWechatAc: true, connectUrl: '/address' }
    })
    .mockResolvedValueOnce({
      data: { accesstokenFech: '', isWechatAc: false, connectUrl: '/address' }
    }),
  wechaApplicationsDetail: jest.fn(() => Promise.resolve({ data: { accesstokenFetch: '', connectUrl: '/address' ,isWechatAc:false} }))
}));
global.window.WXAUTH = {
  replaceTo: jest.fn()
};
useRoute.mockReturnValue({
  query: {
    id: '1'
  }
});
describe('test useUpdateFormFn function', () => {
  let formNode, formData, submit1, copyLink, goBack, getAppInfos;
  beforeEach(async () => {
    const hook = useUpdateFormFn('miniprogram');
    formNode = hook.formNode;
    formData = hook.formData;
    submit1 = hook.submit;
    copyLink = hook.copyLink;
    goBack = hook.goBack;
    getAppInfos = hook.getAppInfo;
    formNode.value = {
      validate: jest.fn()
    };
  });
  afterEach(() => {
    jest.clearAllMocks();
  });
  it('test submit Function and Branches', async () => {
    jest.spyOn(Modal, 'confirm').mockImplementation(params => {
      if (params.onCancel) {
        params.onCancel();
      }
      if (params.onOk) {
        params.onOk();
      }
    });
    await submit1();
    expect(formNode.value.validate).toHaveBeenCalled();
    expect(miniWechatSave).toHaveBeenCalledWith({
      type: 'miniapp',
      logo: '',
      wechatName: '',
      appid: '',
      wechatOriginalId: '',
      url: '',
      isWechatAc: true
    });
    const { submit } = useUpdateFormFn('offiaccount');
    await submit();
    Modal.confirm.mockRestore();
  });
  it('test copyLink func and Branches', async () => {
    jest.spyOn(message, 'success');
    navigator.clipboard = {
      writeText: jest.fn(() => Promise.resolve('ok'))
    };
    await copyLink();
    expect(message.success).toHaveBeenCalledWith('common.copySuccess');
    message.success.mockRestore();
  });
  it('test operationType branches', async () => {
    const { submit, formData } = useUpdateFormFn('miniprogram');
    formData.formItemData.isWechatAc = false;
    jest.spyOn(Modal, 'confirm').mockImplementation(params => {
      if (params.onOk) {
        params.onOk();
      }
    });
    await submit();
    expect(miniWechatSave).toHaveBeenCalledWith({
      type: 'miniapp',
      logo: '',
      wechatName: '',
      appid: '',
      wechatOriginalId: '',
      url: '',
      isWechatAc: false
    });
    Modal.confirm.mockRestore();
  });
  it('test goBack func', async () => {
    goBack();
    expect(window.WXAUTH.replaceTo).toHaveBeenCalled();
  });
  it('test getAppInfo func', async () => {
    getAppInfos();
    getAppInfos(1);
    getAppInfos(2);
    const { getAppInfo, formData } = useUpdateFormFn('offiaccount');
    await getAppInfo(3);
    expect(formData.serverAddress).toBe('');
  });
});
