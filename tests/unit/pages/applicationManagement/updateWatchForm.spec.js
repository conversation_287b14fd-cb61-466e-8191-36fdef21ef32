import { mount } from '@vue/test-utils';
import './mockCommon.js';
import { useRoute } from 'vue-router';
import updateWatchForm from '@/views/pages/applicationManagement/updateWatchForm.vue';
import { Select, Form, Input, Button, Radio,Image } from 'ant-design-vue';
import container from '@/components/modules/container.vue';
jest.mock('@/api/modules/applicationManagement.js', () => ({
  wechaApplicationsSave: jest.fn(() => Promise.resolve({ data: { code: 200 } })),
  wechaApplicationsDetail: jest.fn(() =>
    Promise.resolve({
      code: 200,
      data: {
        id: 1,
        status: 1,
        organizationId: 1,
        appid: '1',
        appkey: 'cust',
        accesstokenFetch: '{"url":"addurl","structure":"access_token"}',
        token: '23333333333333',
        aeskey: '1',
        encryptCode: '1',
        wechatOriginalId: '5555555555',
        type: 'service',
        joinWay: 'open',
        wxid: 'woaap_etocrm',
        connectUrl: null,
        wechatName: 'QQ音乐公众号',
        logo: 'addurl',
        description: 'WOAAP管理平台',
        isWachatAc: false,
        createdAt: '2018-11-15 20:58:58',
        updatedAt: '2024-07-09 09:04:25'
      }
    })
  )
}));
const router = {
  go: jest.fn()
};
describe('test pagination components', () => {
  let wrapper;
  beforeEach(() => {
    wrapper = mount(updateWatchForm, {
      global: {
        components: {
          container,
          'a-form': Form,
          'a-form-item': Form.Item,
          'a-select': Select,
          'a-input': Input,
          'a-input-group': Input.Group,
          'a-radio-group': Radio.Group,
          'a-radio-button': Radio.Button,
          'a-select-option': Select.Option,
          'a-button': Button,
          'a-image':Image
        },
        stubs:['navHeader'],
        directives: {
          debounce: jest.fn(v => v)
        },
        mocks: {
          $router: router
        }
      }
    });
  });
  afterEach(() => {
    jest.resetModules();
  });
  it('test input func and Branches dom', async () => {
    const radioGroupNode = wrapper.findComponent(Radio.Group);
    expect(radioGroupNode.exists()).toBeTruthy();
    await radioGroupNode.vm.$emit('update:value', 0);
    await radioGroupNode.vm.$nextTick();
    expect(radioGroupNode.vm.value).toBe(0);
    const inputNodeArr = wrapper.findAllComponents(Input);
    expect(inputNodeArr.length).toBeGreaterThan(0);
    useRoute.mockReturnValueOnce({
      query: {
        id: 1
      }
    });
    inputNodeArr.forEach(async (el, index) => {
      await el.vm.$emit('update:value', index);
      await el.vm.$nextTick();
      expect(el.vm.value).toEqual(index);
    });
  });
  it('test select dom1', async () => {
    const selectNodeOne = wrapper.findComponent(Select);
    expect(selectNodeOne.exists()).toBeTruthy();
    await selectNodeOne.vm.$emit('update:value', 'o1ther');
  });
});
