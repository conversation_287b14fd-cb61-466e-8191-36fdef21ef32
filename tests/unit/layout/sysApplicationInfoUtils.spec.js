import { getValFun } from '@/views/layout/sysApplicationInfoUtils.js';
Object.defineProperty(global, 'sessionStorage', {
  value: {
    getItem: jest.fn(() => '')
  },
  writable: true
});
jest.mock('@/store', () => {
  return {
    state: {
      systemApplicationInfo: {
        applicationList: [{ menuRoute: '/about' }],
        currApplicationInfo: { menuRoute: '/about' }
      }
    }
  };
});
describe('test sysApplicationInfoUtils fn', () => {
  it('test applicationList Fn', () => {
    const setItemData = '[{"menuRoute":"/home","menuName":"首页"}]';
    jest.spyOn(global.sessionStorage, 'getItem').mockReturnValueOnce(setItemData);
    let datas = getValFun.applicationList();
    expect(datas).toContainEqual({ menuRoute: '/about' });
    jest.spyOn(global.sessionStorage, 'getItem').mockReturnValueOnce('');
    datas = getValFun.applicationList();
  });
  it('test currApplicationInfo Fn', () => {
    const setItemData = '{"menuRoute":"/home","menuName":"首页"}';
    jest.spyOn(global.sessionStorage, 'getItem').mockReturnValueOnce(setItemData);
    let datas = getValFun.currApplicationInfo();
    expect(datas).toEqual({ menuRoute: '/about' });
    jest.spyOn(global.sessionStorage, 'getItem').mockReturnValueOnce('');
    datas = getValFun.currApplicationInfo();
  });
});
