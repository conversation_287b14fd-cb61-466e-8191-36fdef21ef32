jest.unmock('vue-router');
import { mount } from '@vue/test-utils';
import appMainComponent from '@/views/layout/components/appMain.vue';
import { createRouter, createWebHistory } from 'vue-router';
import routerList from '@/router/router.list.js';
import systemApplicationInfo from '@/store/modules/systemApplicationInfo';
import { createStore } from 'vuex';
const router = createRouter({
  history: createWebHistory(),
  routes: routerList
});
const store = createStore({
  modules: {
    systemApplicationInfo
  }
});
describe('test appMain component', () => {
  let wrapper = mount(appMainComponent, {
    global: {
      provide: { store },
      plugins: [router]
    }
  });
  it('test elPopover Funcs', async () => {
    expect(wrapper.html()).toContain('general-app-main-wrapper');
  });
});
