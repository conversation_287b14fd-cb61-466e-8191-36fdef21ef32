import { mount } from '@vue/test-utils';
import indexPage from '@/views/layout/index.vue';
import { Modal, Image, Spin } from 'ant-design-vue';
import appMain from '@/views/layout/components/appMain';
jest.mock('@/views/layout/components/appMain', () => ({
  template: '<div class="appMain-Template">this is appMain</div>'
}));
jest.mock('vuex', () => {
  return {
    useStore: jest.fn(() => {
      return {
        commit: jest.fn()
      };
    })
  };
});
jest.mock('@/views/layout/sysApplicationInfoUtils.js', () => {
  return {
    getValFun: {
      applicationList: jest.fn(() => {
        return [
          {
            id: '1',
            appid: 'xxxx',
            wechatName: 'QQ音乐公众号',
            logo: 'publicPath',
            type: 'service',
            joinWay: 'open',
            createdAt: '2018-11-15 20:58:58',
            updatedAt: '2024-07-09 09:04:25',
            appTypeName: '公众号',
            label: 'QQ音乐公众号',
            value: '1'
          },
          {
            id: '2',
            appid: '3333',
            wechatName: '猫头鹰电商',
            logo: 'publicPath',
            type: 'miniapp',
            joinWay: 'base',
            createdAt: '2020-12-16 18:26:18',
            updatedAt: '2024-07-10 09:59:29',
            appTypeName: '小程序',
            label: '猫头鹰电商',
            value: '2'
          },
          {
            id: '3',
            appid: 'bbbb',
            wechatName: '开云测试公众号',
            logo: 'publicPath',
            type: 'service',
            joinWay: 'base',
            createdAt: null,
            updatedAt: '2024-07-16 05:44:06',
            appTypeName: '公众号',
            label: '开云测试公众号',
            value: '3'
          }
        ];
      }),
      currApplicationInfo: jest.fn(() => {
        return {
          id: '1',
          appid: '1111',
          wechatName: 'QQ音乐公众号',
          logo: 'publicPath',
          type: 'service',
          appTypeName: '公众号'
        };
      })
    }
  };
});
jest.mock('vue-router', () => ({
  onBeforeRouteLeave: jest.fn(),
  useRouter: jest.fn(() => {
    return {
      go: jest.fn()
    };
  }),
  useRoute: jest
    .fn(() => {
      return {
        path: '/cus-wxmsgcenter-fe-web-admin/miniprogram/subscriptionMsgManagement'
      };
    })
    .mockReturnValueOnce({ path: '/cus-wxmsgcenter-fe-web-admin/offiaccount/tplMsgManagement' })
    .mockReturnValueOnce({ path: '' }),
  createRouter: jest.fn(),
  createWebHashHistory: jest.fn()
}));

describe('test layout index page', () => {
  let wrapper;
  beforeEach(() => {
    wrapper = mount(indexPage, {
      global: {
        components: {
          'a-spin': Spin,
          'a-modal': Modal,
          'a-image': Image,
          appMain
        }
      }
    });
  });
  it('test switch app Func and Branch', async () => {
    const linkToNodeLayout = wrapper.find('.general-link-to');
    expect(linkToNodeLayout.exists()).toBeTruthy();
    await linkToNodeLayout.trigger('click');
  });
  it('test applicationList Func and Branch',  () => {
    expect(wrapper.exists()).toBeTruthy();
  });
  it('test Modal app Func and Branch', async () => {
    const modalNodeLayout = wrapper.findComponent(Modal);
    expect(modalNodeLayout.exists()).toBeTruthy();
    await modalNodeLayout.vm.$emit('update:open', true);
    expect(modalNodeLayout.vm.open).toBe(true);
  });
});
