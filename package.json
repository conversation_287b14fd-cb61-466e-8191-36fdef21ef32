{"name": "cus-wxmsgcenter-fe-web-admin", "version": "1.0.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "build:dev": "vue-cli-service build --mode dev", "build:qa": "vue-cli-service build --mode qa", "build:preprod": "vue-cli-service build --mode preprod", "build:prod": "vue-cli-service build --mode prod", "test": "vue-cli-service test:unit --coverage"}, "dependencies": {"@ant-design/icons-vue": "^7.0.1", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.18.6", "@babel/preset-env": "^7.24.7", "@babel/preset-react": "^7.24.7", "@babel/preset-typescript": "^7.24.7", "@vue/cli-plugin-unit-jest": "~5.0.0", "@vue/test-utils": "^2.0.0-0", "@vue/vue3-jest": "^27.0.0", "ant-design-vue": "4.0.0-rc.6", "axios": "^0.21.1", "babel-jest": "^27.0.6", "babel-plugin-import": "^1.13.8", "benz-amr-recorder": "1.0", "clipboard": "^2.0.10", "core-js": "^3.6.5", "echarts": "^5.2.1", "html2canvas": "^1.4.1", "jest": "^27.0.5", "jspdf": "^2.5.1", "less-loader": "^12.2.0", "mitt": "^3.0.0", "moment": "^2.29.2", "qs": "^6.11.0", "terser-webpack-plugin": "^4.2.3", "vue": "3.2.20", "vue-clipboard3": "^2.0.0", "vue-demi": "^0.12.5", "vue-draggable-plus": "^0.3.4", "vue-i18n": "9.2.2", "vue-router": "4.1.0", "vue3-print-nb": "^0.1.4", "vuex": "^4.0.2"}, "devDependencies": {"@babel/plugin-proposal-optional-chaining": "^7.18.9", "@vue/cli-plugin-babel": "^4.5.15", "@vue/cli-plugin-eslint": "^4.5.15", "@vue/cli-plugin-router": "^4.5.15", "@vue/cli-plugin-vuex": "^4.5.15", "@vue/cli-service": "^4.5.15", "@vue/compiler-sfc": "^3.2.23", "babel-eslint": "^10.1.0", "babel-plugin-require-context-hook": "^1.0.0", "babel-plugin-transform-require-context": "^0.1.1", "css-loader": "^7.1.2", "eslint": "^6.7.2", "eslint-plugin-vue": "^7.0.0", "less": "^4.2.0", "mini-css-extract-plugin": "^2.9.0", "postcss-loader": "^8.1.1", "postcss-prefix-selector": "^1.16.1", "sass": "^1.18.0", "sass-loader": "^7.1.0"}, "browserslist": ["> 1%", "last 2 versions", "not dead"], "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/vue3-essential", "eslint:recommended", "plugin:cypress/recommended"], "parserOptions": {"parser": "@babel/eslint-parser"}, "rules": {}}}