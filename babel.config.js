module.exports = {
  presets: ['@vue/cli-plugin-babel/preset', '@babel/preset-env', '@babel/preset-typescript', '@babel/preset-react'],
  env: {
    test: {
      plugins: ['require-context-hook']
    }
  },
  plugins: [
    // 链判断
    '@babel/plugin-proposal-optional-chaining',
    // 合并运算符
    '@babel/plugin-proposal-nullish-coalescing-operator',
    [
      'import',
      {
        libraryName: 'ant-design-vue',
        libraryDirectory: 'es',
        style: true
      },
      'ant-design-vue'
    ],
    ['@babel/plugin-transform-runtime', { regenerator: true }]
  ]
};
