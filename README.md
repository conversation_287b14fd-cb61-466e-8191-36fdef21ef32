
## Project Description

   Main functions of the project: application management, official account template management, applet template management, sending log, central control management
## The main construction tools used in the project

- [node](http://nodejs.org/) and [git](https://git-scm.com/) - Project development environment1
- [Vue3](https://v3.vuejs.org/) - Fami1liar with Vue basic syntax
- [Vue-CLI](https://cli.vuejs.org/) - Familiar with Vue CLI build tools
- [Vue-Router-Next](https://next.router.vuejs.org/) - Familiar w1ith the basic use of vue-router
- [Vuex](https://vuex.vuejs.org/) - Familiar with the basic syntax of data storage in Vuex
- [Ant-Design-Vue](https://antdv.com/docs/vue/introduce-cn/) - ui basic use
- [jest](https://jestjs.io/) - Familiar with the basic syntax of Jest unit testing
- [Vue-Test-Utils](https://test-utils.vuejs.org/) - Familiar with the basic syntax of Vue unit testing

## Recommended IDE Setup

[VSCode](https://code.visualstudio.com/) 

## Customize configuration

See [VueCLi Configuration Reference](https://cli.vuejs.org/config/).

## Environment Requirements

- node.js <= 16.20.2
- npm <= 8.19.4
- pnpm <= 8.15.6
## Project Setup

```sh
pnpm install
```
### Compile and Hot-Reload for Development

```sh
pnpm serve
```
### Compile and Minify for Production

```sh
pnpm build
```
### Run Jest with coverage

```sh
pnpm test:unit
```

## Directory Structure

```Directory
.
├── public                                      # Store static resources（Static resources that do not require construction tool processing）
├── src                                         # Project main directory
│   ├── App.vue                                 # Root component
│   ├── main.js                                 # Entry file
│   ├── qiankunPublicPath.js                    # qiankun Public Path
│   ├── assets                                  # Store static resources（Static resources that need to be processed by building tools）
│   │   ├── css                                 # css
│   │   └── icon                                # Store image resources
│   ├── components                              # Store common components
│   │   ├── index.js                            # Install custom universal components globally in the project
│   │   ├── registerGlobAntdComp.js             # Register ant third-party component library in the project
│   │   └── modules                             # Custom Component Set
│   │       ├── searchForm                      # Query Components
│   │       ├── container.vue                   # Layout the upper, middle, and lower containers of the main area
│   │       ├── pagination.vue                  # Paging component
│   │       └── imgUploaderSingle.vue           # Image upload component
│   ├── router                                  # Store routing configuration
│   │   ├── index.js                            # Route configuration master file
│   │   ├── router.list.js                      # Collection of various routes
│   │   ├── routerPromission.js                 # Routing Guard related permissions
│   │   └── modules                             # Each routing module set
│   │       ├── applicationManagement.js        # Application management module routing
│   │       ├── controlManagement.js            # Central control management module routing
│   │       ├── sendLogs.js                     # Send log module routing
│   │       ├── tplMsgManagement.js             # Routing of official account message management module
│   │       └── subscriptionMsgManagement.js    # Mini program message management module routing
│   ├── stores                                  # Store Vuex status management
│   │   └── modules                             # 
│   │       └── systemApplicationInfo.js        # Application data management
│   ├── utils                                   # Storage tools
│   │   ├── directive.js                        # Customized Vue commands
│   │   └── function.js                         # General method
│   └── views                                   # Store page
│       ├── layout                              # 
│       └── pages                               # Business layer page
│           ├── applicationManagement           # Application management business
│           ├── commonModules                   # General Business
│           ├── controlManagement               # Central control management business
│           ├── sendLogs                        # Sending log business
│           ├── subscriptionMsgManagement       # Mini program message management service
│           └── tplMsgManagement                # Official account message management business
├── tests                                       # Storage of project testing files
│    └── unit                                   # unit testing 
├── .env.development                            # Store development environment variable type declaration files
├── .env.prod                                   # Store formal environment variable type declaration files
├── .env.dev                                    # Store test environment variable type declaration file
├── .eslintrc.js                      
├── .gitignore.js                  
├── .prettierrc.json           
├── babel.config.js                
├── jest.config.js                  
├── pnpm-lock.yaml             
├── README.md                                   # Root Project Description
└── vue.config.ts                               # VueCLi configuration file
```