module.exports = {
  preset: '@vue/cli-plugin-unit-jest',
  collectCoverage: true,
  // testMatch:['**/langIndex.spec.js'],
  // testMatch: ['**/applicationManagement/**/*.spec.js'],
  setupFilesAfterEnv: ['<rootDir>/tests/unit/mockTests/mockMatchMedia.js', '<rootDir>/tests/unit/mockTests/mockModal.js', '<rootDir>/tests/unit/mockTests/mockVueRouter.js'],
  collectCoverageFrom: ['src/**/*.js', 'src/**/*.vue', '!**/node_modules/**'],
  coverageThreshold: {
    global: {
      branches: 80,
      functions: 80,
      lines: 80,
      statements: 80
    }
  },
  coverageReporters: ['cobertura', 'json-summary', 'text', 'html', 'lcov'],
  coverageDirectory: 'coverage/',
  transform: {
    '^.+\\.[tj]sx?$': 'babel-jest',
    '^.+\\.vue$': '@vue/vue3-jest',
  },
  transformIgnorePatterns: [`node_modules/(?!.pnpm|ant-design-vue|@ant-design/icons-vue|@ant-design/icons-svg|axios|@babel/runtime|lodash-es)`]
};
