{"printWidth": 200, "semi": true, "jsxSingleQuote": false, "bracketSpacing": true, "jsxBracketSameLine": false, "arrowParens": "avoid", "insertPragma": false, "tabWidth": 2, "useTabs": false, "endOfLine": "auto", "HTMLWhitespaceSensitivity": "ignore", "extends": ["airbnb", "prettier", "prettier/react"], "singleQuote": true, "prettier.disableLanguages": ["vue"], "trailingComma": "none", "eqeqeq": true}